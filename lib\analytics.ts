import { DatabaseService } from './supabase';

// Analytics event types
export interface AnalyticsEvent {
  name: string;
  properties: Record<string, any>;
  userId?: string;
  sessionId?: string;
  timestamp: Date;
}

export interface PageViewEvent {
  page: string;
  title: string;
  referrer?: string;
  userAgent?: string;
  userId?: string;
}

export interface EnhancementEvent {
  originalPromptLength: number;
  enhancedPromptLength: number;
  appliedRules: string[];
  qualityScore: number;
  processingTime: number;
  userId: string;
}

export interface AIGenerationEvent {
  provider: string;
  model: string;
  promptTokens: number;
  completionTokens: number;
  cost: number;
  responseTime: number;
  userId: string;
}

// Google Analytics 4 integration
export class GoogleAnalytics {
  private measurementId: string;
  private isEnabled: boolean;

  constructor() {
    this.measurementId = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID || '';
    this.isEnabled = !!this.measurementId && typeof window !== 'undefined';
  }

  // Initialize GA4
  init(): void {
    if (!this.isEnabled) return;

    // Load gtag script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${this.measurementId}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
      window.dataLayer.push(args);
    }
    window.gtag = gtag;

    gtag('js', new Date());
    gtag('config', this.measurementId, {
      page_title: document.title,
      page_location: window.location.href,
    });
  }

  // Track page views
  trackPageView(page: string, title: string): void {
    if (!this.isEnabled || !window.gtag) return;

    window.gtag('config', this.measurementId, {
      page_title: title,
      page_location: window.location.href,
    });
  }

  // Track custom events
  trackEvent(eventName: string, parameters: Record<string, any> = {}): void {
    if (!this.isEnabled || !window.gtag) return;

    window.gtag('event', eventName, {
      event_category: parameters.category || 'engagement',
      event_label: parameters.label,
      value: parameters.value,
      ...parameters,
    });
  }

  // Track user properties
  setUserProperties(properties: Record<string, any>): void {
    if (!this.isEnabled || !window.gtag) return;

    window.gtag('config', this.measurementId, {
      custom_map: properties,
    });
  }
}

// Custom analytics service
export class AnalyticsService {
  private db: DatabaseService;
  private ga: GoogleAnalytics;
  private sessionId: string;

  constructor() {
    this.db = new DatabaseService();
    this.ga = new GoogleAnalytics();
    this.sessionId = this.generateSessionId();
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Initialize analytics
  init(): void {
    this.ga.init();
  }

  // Track page views
  async trackPageView(event: PageViewEvent): Promise<void> {
    // Track in Google Analytics
    this.ga.trackPageView(event.page, event.title);

    // Track in custom analytics
    if (event.userId) {
      await this.db.logUserAction(event.userId, 'page_view', {
        page: event.page,
        title: event.title,
        referrer: event.referrer,
        userAgent: event.userAgent,
        sessionId: this.sessionId,
      });
    }
  }

  // Track prompt enhancements
  async trackEnhancement(event: EnhancementEvent): Promise<void> {
    // Track in Google Analytics
    this.ga.trackEvent('prompt_enhanced', {
      category: 'enhancement',
      label: 'prompt_enhancement',
      value: event.qualityScore,
      original_length: event.originalPromptLength,
      enhanced_length: event.enhancedPromptLength,
      processing_time: event.processingTime,
    });

    // Track in custom analytics
    await this.db.logUserAction(event.userId, 'prompt_enhanced', {
      originalPromptLength: event.originalPromptLength,
      enhancedPromptLength: event.enhancedPromptLength,
      appliedRules: event.appliedRules,
      qualityScore: event.qualityScore,
      processingTime: event.processingTime,
      sessionId: this.sessionId,
    });
  }

  // Track AI generations
  async trackAIGeneration(event: AIGenerationEvent): Promise<void> {
    // Track in Google Analytics
    this.ga.trackEvent('ai_generation', {
      category: 'ai',
      label: event.provider,
      value: Math.round(event.cost * 1000), // Convert to cents
      provider: event.provider,
      model: event.model,
      response_time: event.responseTime,
    });

    // Track in custom analytics
    await this.db.logUserAction(event.userId, 'ai_generation', {
      provider: event.provider,
      model: event.model,
      promptTokens: event.promptTokens,
      completionTokens: event.completionTokens,
      cost: event.cost,
      responseTime: event.responseTime,
      sessionId: this.sessionId,
    });
  }

  // Track user registration
  async trackUserRegistration(userId: string, method: string): Promise<void> {
    this.ga.trackEvent('sign_up', {
      category: 'user',
      label: method,
      method,
    });

    await this.db.logUserAction(userId, 'user_registered', {
      method,
      sessionId: this.sessionId,
    });
  }

  // Track subscription changes
  async trackSubscriptionChange(
    userId: string,
    fromTier: string,
    toTier: string
  ): Promise<void> {
    this.ga.trackEvent('subscription_change', {
      category: 'subscription',
      label: `${fromTier}_to_${toTier}`,
      from_tier: fromTier,
      to_tier: toTier,
    });

    await this.db.logUserAction(userId, 'subscription_changed', {
      fromTier,
      toTier,
      sessionId: this.sessionId,
    });
  }

  // Track errors
  async trackError(
    error: Error,
    context: string,
    userId?: string
  ): Promise<void> {
    this.ga.trackEvent('exception', {
      category: 'error',
      description: error.message,
      fatal: false,
      context,
    });

    if (userId) {
      await this.db.logUserAction(userId, 'error_occurred', {
        error: error.message,
        stack: error.stack,
        context,
        sessionId: this.sessionId,
      });
    }
  }

  // Get user analytics
  async getUserAnalytics(
    userId: string,
    startDate: string,
    endDate: string
  ): Promise<{
    totalEnhancements: number;
    totalAIGenerations: number;
    averageQualityScore: number;
    mostUsedProviders: Array<{ provider: string; count: number }>;
    dailyUsage: Array<{ date: string; count: number }>;
  }> {
    const analytics = await this.db.getUserAnalytics(userId, startDate, endDate);

    const enhancements = analytics.filter(a => a.action === 'prompt_enhanced');
    const aiGenerations = analytics.filter(a => a.action === 'ai_generation');

    const qualityScores = enhancements
      .map(e => e.metadata.qualityScore)
      .filter(score => typeof score === 'number');

    const averageQualityScore = qualityScores.length > 0
      ? qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
      : 0;

    const providerCounts = aiGenerations.reduce((acc, gen) => {
      const provider = gen.metadata.provider;
      acc[provider] = (acc[provider] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostUsedProviders = Object.entries(providerCounts)
      .map(([provider, count]) => ({ provider, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Group by date
    const dailyUsage = analytics.reduce((acc, item) => {
      const date = item.created_at.split('T')[0];
      const existing = acc.find(d => d.date === date);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ date, count: 1 });
      }
      return acc;
    }, [] as Array<{ date: string; count: number }>);

    return {
      totalEnhancements: enhancements.length,
      totalAIGenerations: aiGenerations.length,
      averageQualityScore: Math.round(averageQualityScore * 100) / 100,
      mostUsedProviders,
      dailyUsage: dailyUsage.sort((a, b) => a.date.localeCompare(b.date)),
    };
  }

  // Performance tracking
  trackPerformance(name: string, duration: number, userId?: string): void {
    this.ga.trackEvent('timing_complete', {
      category: 'performance',
      name,
      value: Math.round(duration),
    });

    if (userId) {
      this.db.logUserAction(userId, 'performance_metric', {
        name,
        duration,
        sessionId: this.sessionId,
      });
    }
  }
}

// Singleton instance
let analyticsInstance: AnalyticsService | null = null;

export function getAnalytics(): AnalyticsService {
  if (!analyticsInstance) {
    analyticsInstance = new AnalyticsService();
  }
  return analyticsInstance;
}

// React hook for analytics
export function useAnalytics() {
  const analytics = getAnalytics();

  return {
    trackPageView: analytics.trackPageView.bind(analytics),
    trackEnhancement: analytics.trackEnhancement.bind(analytics),
    trackAIGeneration: analytics.trackAIGeneration.bind(analytics),
    trackError: analytics.trackError.bind(analytics),
    trackPerformance: analytics.trackPerformance.bind(analytics),
  };
}

// Utility functions
export function withAnalytics<T extends (...args: any[]) => any>(
  fn: T,
  eventName: string,
  userId?: string
): T {
  return ((...args: any[]) => {
    const start = performance.now();
    const result = fn(...args);
    const duration = performance.now() - start;

    const analytics = getAnalytics();
    analytics.trackPerformance(eventName, duration, userId);

    return result;
  }) as T;
}

// Global error tracking
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    const analytics = getAnalytics();
    analytics.trackError(
      new Error(event.message),
      `${event.filename}:${event.lineno}:${event.colno}`
    );
  });

  window.addEventListener('unhandledrejection', (event) => {
    const analytics = getAnalytics();
    analytics.trackError(
      new Error(event.reason),
      'unhandled_promise_rejection'
    );
  });
}
