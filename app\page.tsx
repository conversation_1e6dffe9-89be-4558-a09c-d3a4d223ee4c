import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowR<PERSON>, Sparkles, Zap, Shield, BarChart3 } from 'lucide-react';

import { generateMetadata, PAGE_SEO } from '@/lib/seo';
import { PromptEnhancer } from '@/components/PromptEnhancer';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = generateMetadata(PAGE_SEO.home);

export default function HomePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="section-lg bg-gradient-to-br from-primary/5 via-background to-secondary/5">
        <div className="container-wide">
          <div className="mx-auto max-w-4xl text-center">
            <div className="mb-6 inline-flex items-center rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary">
              <Sparkles className="mr-2 h-4 w-4" />
              Transform Your AI Prompts
            </div>
            
            <h1 className="mb-6 text-balance">
              Get Better AI Responses with{' '}
              <span className="text-gradient">Enhanced Prompts</span>
            </h1>
            
            <p className="mb-8 text-xl text-muted-foreground text-pretty">
              Our advanced algorithms analyze and improve your prompts to generate 
              better responses from ChatGPT, Claude, Gemini, and other AI models. 
              Free to use with premium features available.
            </p>
            
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" className="group">
                Try Free Enhancement
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
              
              <Button variant="outline" size="lg" asChild>
                <Link href="/dashboard">View Dashboard</Link>
              </Button>
            </div>
            
            <div className="mt-12 grid grid-cols-2 gap-8 sm:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">50K+</div>
                <div className="text-sm text-muted-foreground">Prompts Enhanced</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">95%</div>
                <div className="text-sm text-muted-foreground">Quality Improvement</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">4.8/5</div>
                <div className="text-sm text-muted-foreground">User Rating</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">24/7</div>
                <div className="text-sm text-muted-foreground">Available</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Prompt Enhancer Demo */}
      <section className="section">
        <div className="container-narrow">
          <div className="mb-12 text-center">
            <h2 className="mb-4">Try It Now</h2>
            <p className="text-lg text-muted-foreground">
              Enter your prompt below and see how our AI enhancement engine improves it
            </p>
          </div>
          
          <PromptEnhancer />
        </div>
      </section>

      {/* Features Section */}
      <section className="section bg-muted/30">
        <div className="container-wide">
          <div className="mb-16 text-center">
            <h2 className="mb-4">Why Choose AI Prompt Enhancer?</h2>
            <p className="text-lg text-muted-foreground">
              Powerful features designed to maximize your AI interactions
            </p>
          </div>
          
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <div className="card hover-lift">
              <div className="card-content">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <h3 className="mb-2 text-xl font-semibold">Instant Enhancement</h3>
                <p className="text-muted-foreground">
                  Get improved prompts in seconds with our advanced AI algorithms 
                  that analyze context, clarity, and effectiveness.
                </p>
              </div>
            </div>
            
            <div className="card hover-lift">
              <div className="card-content">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <h3 className="mb-2 text-xl font-semibold">Multi-AI Support</h3>
                <p className="text-muted-foreground">
                  Optimized for ChatGPT, Claude, Gemini, and other popular AI models. 
                  Get the best results regardless of your preferred platform.
                </p>
              </div>
            </div>
            
            <div className="card hover-lift">
              <div className="card-content">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                  <BarChart3 className="h-6 w-6 text-primary" />
                </div>
                <h3 className="mb-2 text-xl font-semibold">Quality Analytics</h3>
                <p className="text-muted-foreground">
                  Track improvement scores, analyze patterns, and optimize your 
                  prompting strategy with detailed analytics.
                </p>
              </div>
            </div>
            
            <div className="card hover-lift">
              <div className="card-content">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                  <Sparkles className="h-6 w-6 text-primary" />
                </div>
                <h3 className="mb-2 text-xl font-semibold">Template Library</h3>
                <p className="text-muted-foreground">
                  Access hundreds of proven prompt templates for writing, coding, 
                  analysis, and creative tasks.
                </p>
              </div>
            </div>
            
            <div className="card hover-lift">
              <div className="card-content">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <h3 className="mb-2 text-xl font-semibold">Privacy First</h3>
                <p className="text-muted-foreground">
                  Your prompts are processed securely and never stored without 
                  permission. Full GDPR compliance and data protection.
                </p>
              </div>
            </div>
            
            <div className="card hover-lift">
              <div className="card-content">
                <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <h3 className="mb-2 text-xl font-semibold">Free Tier</h3>
                <p className="text-muted-foreground">
                  Start with 50 free enhancements per day. Upgrade for unlimited 
                  access and advanced features.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="section">
        <div className="container-narrow">
          <div className="mb-16 text-center">
            <h2 className="mb-4">How It Works</h2>
            <p className="text-lg text-muted-foreground">
              Three simple steps to better AI responses
            </p>
          </div>
          
          <div className="grid gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground text-xl font-bold">
                1
              </div>
              <h3 className="mb-2 text-lg font-semibold">Enter Your Prompt</h3>
              <p className="text-muted-foreground">
                Type or paste your original prompt into our enhancement tool
              </p>
            </div>
            
            <div className="text-center">
              <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground text-xl font-bold">
                2
              </div>
              <h3 className="mb-2 text-lg font-semibold">AI Enhancement</h3>
              <p className="text-muted-foreground">
                Our algorithms analyze and improve your prompt for better clarity and results
              </p>
            </div>
            
            <div className="text-center">
              <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground text-xl font-bold">
                3
              </div>
              <h3 className="mb-2 text-lg font-semibold">Get Better Results</h3>
              <p className="text-muted-foreground">
                Use the enhanced prompt with any AI model for improved responses
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary text-primary-foreground">
        <div className="container-narrow text-center">
          <h2 className="mb-4 text-white">Ready to Enhance Your AI Prompts?</h2>
          <p className="mb-8 text-xl text-primary-foreground/90">
            Join thousands of users who are getting better AI responses every day
          </p>
          
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
            <Button size="lg" variant="secondary" className="group">
              Start Free Trial
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
            
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
              View Pricing
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
