'use client';

import { useState, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, BarChart3, Clock, Zap } from 'lucide-react';

import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import type { EnhancementResponse } from '@/types';

interface PromptEnhancerProps {
  className?: string;
  onEnhancement?: (result: EnhancementResponse) => void;
}

export function PromptEnhancer({ className, onEnhancement }: PromptEnhancerProps) {
  const [prompt, setPrompt] = useState('');
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [result, setResult] = useState<EnhancementResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const enhancePrompt = useCallback(async () => {
    if (!prompt.trim()) {
      toast.error('Please enter a prompt to enhance');
      return;
    }

    if (prompt.length > 10000) {
      toast.error('Prompt is too long. Maximum 10,000 characters allowed.');
      return;
    }

    setIsEnhancing(true);
    setError(null);

    try {
      const response = await fetch('/api/enhance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          context: {
            userLevel: 'intermediate',
            targetAI: 'general',
          },
          options: {
            enhancementLevel: 'basic',
            includeExamples: false,
          },
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || 'Enhancement failed');
      }

      if (data.success && data.data) {
        setResult(data.data);
        onEnhancement?.(data.data);
        toast.success('Prompt enhanced successfully!');
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsEnhancing(false);
    }
  }, [prompt, onEnhancement]);

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  }, []);

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getQualityLabel = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 75) return 'Good';
    if (score >= 60) return 'Fair';
    return 'Needs Improvement';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Input Section */}
      <div className="card">
        <div className="card-content">
          <div className="space-y-4">
            <div>
              <label htmlFor="prompt-input" className="block text-sm font-medium mb-2">
                Enter your prompt
              </label>
              <textarea
                id="prompt-input"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Type your prompt here... For example: 'Write a blog post about AI'"
                className="w-full min-h-[120px] p-3 border border-input rounded-md resize-vertical focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                disabled={isEnhancing}
              />
              <div className="flex justify-between items-center mt-2 text-sm text-muted-foreground">
                <span>{prompt.length}/10,000 characters</span>
                {prompt.length > 9000 && (
                  <span className="text-yellow-600">Approaching character limit</span>
                )}
              </div>
            </div>

            <Button
              onClick={enhancePrompt}
              disabled={!prompt.trim() || isEnhancing}
              className="w-full sm:w-auto group"
              size="lg"
            >
              {isEnhancing ? (
                <>
                  <div className="spinner mr-2 h-4 w-4" />
                  Enhancing...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4 transition-transform group-hover:scale-110" />
                  Enhance Prompt
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="card border-destructive/50 bg-destructive/5">
          <div className="card-content">
            <div className="flex items-center text-destructive">
              <span className="font-medium">Error:</span>
              <span className="ml-2">{error}</span>
            </div>
          </div>
        </div>
      )}

      {/* Results Section */}
      {result && (
        <div className="space-y-6">
          {/* Quality Metrics */}
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
            <div className="card text-center">
              <div className="card-content py-4">
                <div className={`text-2xl font-bold ${getQualityColor(result.qualityScore)}`}>
                  {result.qualityScore}%
                </div>
                <div className="text-sm text-muted-foreground">Quality Score</div>
                <div className={`text-xs font-medium ${getQualityColor(result.qualityScore)}`}>
                  {getQualityLabel(result.qualityScore)}
                </div>
              </div>
            </div>

            <div className="card text-center">
              <div className="card-content py-4">
                <div className="text-2xl font-bold text-primary">
                  {result.estimatedTokens}
                </div>
                <div className="text-sm text-muted-foreground">Est. Tokens</div>
              </div>
            </div>

            <div className="card text-center">
              <div className="card-content py-4">
                <div className="text-2xl font-bold text-primary">
                  {result.appliedRules.length}
                </div>
                <div className="text-sm text-muted-foreground">Rules Applied</div>
              </div>
            </div>

            <div className="card text-center">
              <div className="card-content py-4">
                <div className="text-2xl font-bold text-primary">
                  {result.processingTime}ms
                </div>
                <div className="text-sm text-muted-foreground">Processing Time</div>
              </div>
            </div>
          </div>

          {/* Enhanced Prompt */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center">
                  <Zap className="mr-2 h-5 w-5 text-primary" />
                  Enhanced Prompt
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(result.enhancedPrompt)}
                  className="group"
                >
                  <Copy className="mr-2 h-4 w-4 transition-transform group-hover:scale-110" />
                  Copy
                </Button>
              </div>
            </div>
            <div className="card-content">
              <div className="bg-muted/50 p-4 rounded-md">
                <p className="whitespace-pre-wrap text-sm leading-relaxed">
                  {result.enhancedPrompt}
                </p>
              </div>
            </div>
          </div>

          {/* Applied Rules */}
          {result.appliedRules.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold flex items-center">
                  <BarChart3 className="mr-2 h-5 w-5 text-primary" />
                  Applied Enhancements
                </h3>
              </div>
              <div className="card-content">
                <div className="flex flex-wrap gap-2">
                  {result.appliedRules.map((rule, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary"
                    >
                      {rule.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Suggestions */}
          {result.suggestions.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold">Suggestions for Further Improvement</h3>
              </div>
              <div className="card-content">
                <ul className="space-y-2">
                  {result.suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start">
                      <span className="inline-block w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0" />
                      <span className="text-sm text-muted-foreground">{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Comparison */}
          <div className="grid gap-6 lg:grid-cols-2">
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-muted-foreground">Original Prompt</h3>
              </div>
              <div className="card-content">
                <div className="bg-muted/30 p-4 rounded-md">
                  <p className="whitespace-pre-wrap text-sm leading-relaxed text-muted-foreground">
                    {prompt}
                  </p>
                </div>
                <div className="mt-2 text-xs text-muted-foreground">
                  {prompt.length} characters
                </div>
              </div>
            </div>

            <div className="card border-primary/20 bg-primary/5">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-primary">Enhanced Prompt</h3>
              </div>
              <div className="card-content">
                <div className="bg-background p-4 rounded-md border">
                  <p className="whitespace-pre-wrap text-sm leading-relaxed">
                    {result.enhancedPrompt}
                  </p>
                </div>
                <div className="mt-2 text-xs text-muted-foreground">
                  {result.enhancedPrompt.length} characters
                  <span className="ml-2 text-primary">
                    (+{result.enhancedPrompt.length - prompt.length} chars)
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
