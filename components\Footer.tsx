'use client';

import Link from 'next/link';
import { 
  Github, 
  Twitter, 
  Linkedin, 
  Mail, 
  Sparkles,
  ExternalLink,
  Heart
} from 'lucide-react';

import { SOCIAL_LINKS, EXTERNAL_LINKS, APP_METADATA } from '@/utils/constants';

interface FooterLink {
  name: string;
  href: string;
  external?: boolean;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

const footerSections: FooterSection[] = [
  {
    title: 'Product',
    links: [
      { name: 'Features', href: '/#features' },
      { name: 'Pricing', href: '/pricing' },
      { name: 'Templates', href: '/templates' },
      { name: 'API', href: '/api-docs', external: true },
    ],
  },
  {
    title: 'Resources',
    links: [
      { name: 'Documentation', href: EXTERNAL_LINKS.DOCUMENTATION, external: true },
      { name: 'Blog', href: EXTERNAL_LINKS.BLOG, external: true },
      { name: 'Changelog', href: EXTERNAL_LINKS.CHANGELOG, external: true },
      { name: 'Status', href: EXTERNAL_LINKS.STATUS, external: true },
    ],
  },
  {
    title: 'Support',
    links: [
      { name: 'Help Center', href: EXTERNAL_LINKS.SUPPORT, external: true },
      { name: 'Contact', href: '/contact' },
      { name: 'Community', href: '/community' },
      { name: 'Report Bug', href: '/bug-report' },
    ],
  },
  {
    title: 'Company',
    links: [
      { name: 'About', href: '/about' },
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Cookie Policy', href: '/cookies' },
    ],
  },
];

const socialLinks = [
  {
    name: 'GitHub',
    href: SOCIAL_LINKS.GITHUB,
    icon: <Github className="h-5 w-5" />,
    hoverColor: 'hover:text-gray-900 dark:hover:text-white',
  },
  {
    name: 'Twitter',
    href: SOCIAL_LINKS.TWITTER,
    icon: <Twitter className="h-5 w-5" />,
    hoverColor: 'hover:text-blue-500',
  },
  {
    name: 'LinkedIn',
    href: SOCIAL_LINKS.LINKEDIN,
    icon: <Linkedin className="h-5 w-5" />,
    hoverColor: 'hover:text-blue-600',
  },
  {
    name: 'Email',
    href: 'mailto:<EMAIL>',
    icon: <Mail className="h-5 w-5" />,
    hoverColor: 'hover:text-green-600',
  },
];

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-background border-t">
      <div className="container-wide">
        {/* Main Footer Content */}
        <div className="py-12 lg:py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <Link href="/" className="flex items-center space-x-2 mb-4">
                <Sparkles className="h-6 w-6 text-primary" />
                <span className="font-bold text-xl">{APP_METADATA.NAME}</span>
              </Link>
              
              <p className="text-muted-foreground text-sm mb-6 max-w-sm">
                {APP_METADATA.DESCRIPTION}. Enhance your AI interactions with 
                intelligent prompt optimization.
              </p>
              
              {/* Social Links */}
              <div className="flex space-x-4">
                {socialLinks.map((social) => (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`text-muted-foreground transition-colors ${social.hoverColor}`}
                    aria-label={`Follow us on ${social.name}`}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>

            {/* Footer Links */}
            {footerSections.map((section) => (
              <div key={section.title} className="lg:col-span-1">
                <h3 className="font-semibold text-sm mb-4">{section.title}</h3>
                <ul className="space-y-3">
                  {section.links.map((link) => (
                    <li key={link.name}>
                      {link.external ? (
                        <a
                          href={link.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-muted-foreground hover:text-foreground transition-colors inline-flex items-center group"
                        >
                          {link.name}
                          <ExternalLink className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                        </a>
                      ) : (
                        <Link
                          href={link.href}
                          className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                        >
                          {link.name}
                        </Link>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="border-t py-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex-1">
              <h3 className="font-semibold text-sm mb-2">Stay updated</h3>
              <p className="text-sm text-muted-foreground">
                Get the latest updates on new features and improvements.
              </p>
            </div>
            
            <div className="flex-1 max-w-md">
              <form className="flex gap-2" onSubmit={(e) => e.preventDefault()}>
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-3 py-2 text-sm border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  required
                />
                <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                >
                  Subscribe
                </button>
              </form>
              <p className="text-xs text-muted-foreground mt-2">
                We respect your privacy. Unsubscribe at any time.
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex flex-col md:flex-row md:items-center gap-4 text-sm text-muted-foreground">
              <p>
                © {currentYear} {APP_METADATA.NAME}. All rights reserved.
              </p>
              
              <div className="flex items-center gap-4">
                <Link 
                  href="/privacy" 
                  className="hover:text-foreground transition-colors"
                >
                  Privacy
                </Link>
                <Link 
                  href="/terms" 
                  className="hover:text-foreground transition-colors"
                >
                  Terms
                </Link>
                <Link 
                  href="/cookies" 
                  className="hover:text-foreground transition-colors"
                >
                  Cookies
                </Link>
              </div>
            </div>
            
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <span>Made with</span>
              <Heart className="h-4 w-4 text-red-500 fill-current" />
              <span>by</span>
              <a
                href={SOCIAL_LINKS.GITHUB}
                target="_blank"
                rel="noopener noreferrer"
                className="font-medium hover:text-foreground transition-colors"
              >
                {APP_METADATA.AUTHOR}
              </a>
            </div>
          </div>
        </div>

        {/* Version Info (Development Only) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="border-t py-4">
            <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground">
              <span>Version: {APP_METADATA.VERSION}</span>
              <span>•</span>
              <span>Environment: {process.env.NODE_ENV}</span>
              <span>•</span>
              <span>Build: {process.env.VERCEL_GIT_COMMIT_SHA?.slice(0, 7) || 'local'}</span>
            </div>
          </div>
        )}
      </div>

      {/* Structured Data for Organization */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Organization',
            name: APP_METADATA.NAME,
            url: process.env.NEXT_PUBLIC_SITE_URL,
            logo: `${process.env.NEXT_PUBLIC_SITE_URL}/logo.png`,
            description: APP_METADATA.DESCRIPTION,
            founder: {
              '@type': 'Person',
              name: APP_METADATA.AUTHOR,
            },
            sameAs: [
              SOCIAL_LINKS.TWITTER,
              SOCIAL_LINKS.GITHUB,
              SOCIAL_LINKS.LINKEDIN,
            ],
            contactPoint: {
              '@type': 'ContactPoint',
              contactType: 'customer service',
              email: '<EMAIL>',
              availableLanguage: 'English',
            },
          }),
        }}
      />
    </footer>
  );
}
