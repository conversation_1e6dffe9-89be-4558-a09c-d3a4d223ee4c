// Database types for Supabase integration

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          created_at: string;
          subscription_tier: 'free' | 'pro' | 'business';
          monthly_usage: number;
          last_reset_date: string;
        };
        Insert: {
          id?: string;
          email: string;
          created_at?: string;
          subscription_tier?: 'free' | 'pro' | 'business';
          monthly_usage?: number;
          last_reset_date?: string;
        };
        Update: {
          id?: string;
          email?: string;
          created_at?: string;
          subscription_tier?: 'free' | 'pro' | 'business';
          monthly_usage?: number;
          last_reset_date?: string;
        };
      };
      user_profiles: {
        Row: {
          id: string;
          user_id: string;
          full_name: string | null;
          avatar_url: string | null;
          website: string | null;
          bio: string | null;
          preferences: {
            theme: 'light' | 'dark' | 'system';
            defaultAiProvider: string;
            enhancementLevel: 'basic' | 'advanced' | 'expert';
            emailNotifications: boolean;
            analyticsEnabled: boolean;
          };
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          full_name?: string | null;
          avatar_url?: string | null;
          website?: string | null;
          bio?: string | null;
          preferences?: {
            theme?: 'light' | 'dark' | 'system';
            defaultAiProvider?: string;
            enhancementLevel?: 'basic' | 'advanced' | 'expert';
            emailNotifications?: boolean;
            analyticsEnabled?: boolean;
          };
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          website?: string | null;
          bio?: string | null;
          preferences?: {
            theme?: 'light' | 'dark' | 'system';
            defaultAiProvider?: string;
            enhancementLevel?: 'basic' | 'advanced' | 'expert';
            emailNotifications?: boolean;
            analyticsEnabled?: boolean;
          };
          created_at?: string;
          updated_at?: string;
        };
      };
      prompt_enhancements: {
        Row: {
          id: string;
          user_id: string;
          original_prompt: string;
          enhanced_prompt: string;
          ai_provider: string | null;
          response_quality_score: number | null;
          applied_rules: string[];
          estimated_tokens: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          original_prompt: string;
          enhanced_prompt: string;
          ai_provider?: string | null;
          response_quality_score?: number | null;
          applied_rules?: string[];
          estimated_tokens?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          original_prompt?: string;
          enhanced_prompt?: string;
          ai_provider?: string | null;
          response_quality_score?: number | null;
          applied_rules?: string[];
          estimated_tokens?: number;
          created_at?: string;
        };
      };
      prompt_templates: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          category: string;
          template_text: string;
          variables: string[];
          usage_count: number;
          is_public: boolean;
          created_by: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          category: string;
          template_text: string;
          variables?: string[];
          usage_count?: number;
          is_public?: boolean;
          created_by?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          category?: string;
          template_text?: string;
          variables?: string[];
          usage_count?: number;
          is_public?: boolean;
          created_by?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      usage_analytics: {
        Row: {
          id: string;
          user_id: string;
          action: string;
          metadata: Record<string, any>;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          action: string;
          metadata?: Record<string, any>;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          action?: string;
          metadata?: Record<string, any>;
          created_at?: string;
        };
      };
    };
    Views: {
      user_stats: {
        Row: {
          user_id: string;
          total_enhancements: number;
          avg_quality_score: number;
          most_used_provider: string;
          last_activity: string;
        };
      };
      popular_templates: {
        Row: {
          id: string;
          name: string;
          category: string;
          usage_count: number;
          avg_rating: number;
        };
      };
    };
    Functions: {
      increment_user_usage: {
        Args: {
          user_id: string;
          increment_by?: number;
        };
        Returns: void;
      };
      increment_template_usage: {
        Args: {
          template_id: string;
        };
        Returns: void;
      };
      get_user_analytics: {
        Args: {
          user_id: string;
          start_date: string;
          end_date: string;
        };
        Returns: {
          date: string;
          enhancements: number;
          ai_generations: number;
          avg_quality: number;
        }[];
      };
      reset_monthly_usage: {
        Args: {
          user_id: string;
        };
        Returns: void;
      };
      get_popular_templates: {
        Args: {
          category?: string;
          limit?: number;
        };
        Returns: {
          id: string;
          name: string;
          category: string;
          usage_count: number;
          template_text: string;
        }[];
      };
    };
    Enums: {
      subscription_tier: 'free' | 'pro' | 'business';
      theme_preference: 'light' | 'dark' | 'system';
      enhancement_level: 'basic' | 'advanced' | 'expert';
    };
  };
}

// Type helpers for database operations
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];

// Specific table types
export type UserRow = Tables<'users'>;
export type UserInsert = TablesInsert<'users'>;
export type UserUpdate = TablesUpdate<'users'>;

export type UserProfileRow = Tables<'user_profiles'>;
export type UserProfileInsert = TablesInsert<'user_profiles'>;
export type UserProfileUpdate = TablesUpdate<'user_profiles'>;

export type PromptEnhancementRow = Tables<'prompt_enhancements'>;
export type PromptEnhancementInsert = TablesInsert<'prompt_enhancements'>;
export type PromptEnhancementUpdate = TablesUpdate<'prompt_enhancements'>;

export type PromptTemplateRow = Tables<'prompt_templates'>;
export type PromptTemplateInsert = TablesInsert<'prompt_templates'>;
export type PromptTemplateUpdate = TablesUpdate<'prompt_templates'>;

export type UsageAnalyticsRow = Tables<'usage_analytics'>;
export type UsageAnalyticsInsert = TablesInsert<'usage_analytics'>;
export type UsageAnalyticsUpdate = TablesUpdate<'usage_analytics'>;

// View types
export type UserStatsView = Database['public']['Views']['user_stats']['Row'];
export type PopularTemplatesView = Database['public']['Views']['popular_templates']['Row'];

// Function types
export type IncrementUserUsageArgs = Database['public']['Functions']['increment_user_usage']['Args'];
export type IncrementTemplateUsageArgs = Database['public']['Functions']['increment_template_usage']['Args'];
export type GetUserAnalyticsArgs = Database['public']['Functions']['get_user_analytics']['Args'];
export type GetUserAnalyticsReturn = Database['public']['Functions']['get_user_analytics']['Returns'];
export type ResetMonthlyUsageArgs = Database['public']['Functions']['reset_monthly_usage']['Args'];
export type GetPopularTemplatesArgs = Database['public']['Functions']['get_popular_templates']['Args'];
export type GetPopularTemplatesReturn = Database['public']['Functions']['get_popular_templates']['Returns'];

// Enum types
export type SubscriptionTier = Database['public']['Enums']['subscription_tier'];
export type ThemePreference = Database['public']['Enums']['theme_preference'];
export type EnhancementLevel = Database['public']['Enums']['enhancement_level'];

// Composite types for joins
export interface UserWithProfile extends UserRow {
  profile?: UserProfileRow;
}

export interface EnhancementWithUser extends PromptEnhancementRow {
  user?: UserRow;
}

export interface TemplateWithCreator extends PromptTemplateRow {
  creator?: UserRow;
}

// Query result types
export interface PaginatedResult<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface AnalyticsResult {
  totalEnhancements: number;
  totalAIGenerations: number;
  averageQualityScore: number;
  mostUsedProviders: Array<{ provider: string; count: number }>;
  dailyUsage: Array<{ date: string; count: number }>;
}

// Database error types
export interface DatabaseError {
  message: string;
  details: string;
  hint: string;
  code: string;
}

// RPC (Remote Procedure Call) types
export interface RPCResponse<T = any> {
  data: T | null;
  error: DatabaseError | null;
}

// Subscription and billing related types (for future use)
export interface SubscriptionInfo {
  tier: SubscriptionTier;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
}

export interface UsageQuota {
  enhancements: {
    used: number;
    limit: number;
    resetDate: string;
  };
  aiGenerations: {
    used: number;
    limit: number;
    resetDate: string;
  };
}

// Search and filtering types
export interface DatabaseFilters {
  userId?: string;
  category?: string;
  provider?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  qualityScore?: {
    min: number;
    max: number;
  };
  isPublic?: boolean;
}

export interface SortOptions {
  column: string;
  ascending: boolean;
}

// Batch operation types
export interface BatchInsert<T> {
  table: keyof Database['public']['Tables'];
  data: T[];
}

export interface BatchUpdate<T> {
  table: keyof Database['public']['Tables'];
  updates: Array<{
    id: string;
    data: T;
  }>;
}

export interface BatchDelete {
  table: keyof Database['public']['Tables'];
  ids: string[];
}
