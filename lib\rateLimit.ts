import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

// Rate limit configurations for different tiers
export interface RateLimitConfig {
  requests: number;
  window: string;
  identifier: string;
}

export interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}

// Subscription tiers and their limits
export const RATE_LIMITS = {
  free: {
    enhancePrompt: { requests: 50, window: '1 d' }, // 50 per day
    aiGeneration: { requests: 10, window: '1 d' }, // 10 AI calls per day
    apiCalls: { requests: 100, window: '1 h' }, // 100 API calls per hour
  },
  pro: {
    enhancePrompt: { requests: 500, window: '1 d' }, // 500 per day
    aiGeneration: { requests: 100, window: '1 d' }, // 100 AI calls per day
    apiCalls: { requests: 1000, window: '1 h' }, // 1000 API calls per hour
  },
  business: {
    enhancePrompt: { requests: 5000, window: '1 d' }, // 5000 per day
    aiGeneration: { requests: 1000, window: '1 d' }, // 1000 AI calls per day
    apiCalls: { requests: 10000, window: '1 h' }, // 10000 API calls per hour
  },
} as const;

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

// Create rate limiters for different actions
const rateLimiters = {
  // Free tier limiters
  freeEnhancePrompt: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(50, '1 d'),
    analytics: true,
    prefix: 'ratelimit:free:enhance',
  }),
  freeAiGeneration: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(10, '1 d'),
    analytics: true,
    prefix: 'ratelimit:free:ai',
  }),
  freeApiCalls: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(100, '1 h'),
    analytics: true,
    prefix: 'ratelimit:free:api',
  }),

  // Pro tier limiters
  proEnhancePrompt: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(500, '1 d'),
    analytics: true,
    prefix: 'ratelimit:pro:enhance',
  }),
  proAiGeneration: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(100, '1 d'),
    analytics: true,
    prefix: 'ratelimit:pro:ai',
  }),
  proApiCalls: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(1000, '1 h'),
    analytics: true,
    prefix: 'ratelimit:pro:api',
  }),

  // Business tier limiters
  businessEnhancePrompt: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(5000, '1 d'),
    analytics: true,
    prefix: 'ratelimit:business:enhance',
  }),
  businessAiGeneration: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(1000, '1 d'),
    analytics: true,
    prefix: 'ratelimit:business:ai',
  }),
  businessApiCalls: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(10000, '1 h'),
    analytics: true,
    prefix: 'ratelimit:business:api',
  }),

  // Global rate limiter for abuse prevention
  global: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(1000, '1 m'),
    analytics: true,
    prefix: 'ratelimit:global',
  }),
};

// Rate limiting service
export class RateLimitService {
  private static instance: RateLimitService;

  private constructor() {}

  public static getInstance(): RateLimitService {
    if (!RateLimitService.instance) {
      RateLimitService.instance = new RateLimitService();
    }
    return RateLimitService.instance;
  }

  async checkRateLimit(
    identifier: string,
    action: 'enhancePrompt' | 'aiGeneration' | 'apiCalls',
    tier: 'free' | 'pro' | 'business' = 'free'
  ): Promise<RateLimitResult> {
    try {
      // Check global rate limit first
      const globalCheck = await rateLimiters.global.limit(identifier);
      if (!globalCheck.success) {
        return {
          success: false,
          limit: globalCheck.limit,
          remaining: globalCheck.remaining,
          reset: globalCheck.reset,
          retryAfter: Math.ceil((globalCheck.reset.getTime() - Date.now()) / 1000),
        };
      }

      // Check specific action rate limit
      const limiterKey = `${tier}${action.charAt(0).toUpperCase() + action.slice(1)}` as keyof typeof rateLimiters;
      const limiter = rateLimiters[limiterKey];
      
      if (!limiter) {
        throw new Error(`Rate limiter not found for ${tier}:${action}`);
      }

      const result = await limiter.limit(identifier);
      
      return {
        success: result.success,
        limit: result.limit,
        remaining: result.remaining,
        reset: result.reset,
        retryAfter: result.success ? undefined : Math.ceil((result.reset.getTime() - Date.now()) / 1000),
      };
    } catch (error) {
      console.error('Rate limit check failed:', error);
      // Fail open - allow the request if rate limiting fails
      return {
        success: true,
        limit: 1000,
        remaining: 999,
        reset: new Date(Date.now() + 3600000), // 1 hour from now
      };
    }
  }

  async getRemainingQuota(
    identifier: string,
    action: 'enhancePrompt' | 'aiGeneration' | 'apiCalls',
    tier: 'free' | 'pro' | 'business' = 'free'
  ): Promise<{ remaining: number; limit: number; resetTime: Date }> {
    const result = await this.checkRateLimit(identifier, action, tier);
    return {
      remaining: result.remaining,
      limit: result.limit,
      resetTime: result.reset,
    };
  }

  async resetUserLimits(identifier: string): Promise<void> {
    try {
      const keys = await redis.keys(`ratelimit:*:${identifier}`);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.error('Failed to reset user limits:', error);
    }
  }

  async getAnalytics(
    timeframe: '1h' | '24h' | '7d' = '24h'
  ): Promise<{
    totalRequests: number;
    blockedRequests: number;
    topUsers: Array<{ identifier: string; requests: number }>;
  }> {
    try {
      // This is a simplified analytics implementation
      // In production, you might want to use a more sophisticated analytics system
      const analyticsKey = `analytics:${timeframe}`;
      const data = await redis.get(analyticsKey);
      
      if (data) {
        return JSON.parse(data as string);
      }

      return {
        totalRequests: 0,
        blockedRequests: 0,
        topUsers: [],
      };
    } catch (error) {
      console.error('Failed to get analytics:', error);
      return {
        totalRequests: 0,
        blockedRequests: 0,
        topUsers: [],
      };
    }
  }
}

// Utility functions
export function getRateLimitHeaders(result: RateLimitResult): Record<string, string> {
  return {
    'X-RateLimit-Limit': result.limit.toString(),
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': result.reset.toISOString(),
    ...(result.retryAfter && { 'Retry-After': result.retryAfter.toString() }),
  };
}

export function createRateLimitError(result: RateLimitResult): Error {
  const error = new Error(`Rate limit exceeded. Try again in ${result.retryAfter} seconds.`);
  (error as any).statusCode = 429;
  (error as any).headers = getRateLimitHeaders(result);
  return error;
}

// IP-based rate limiting for anonymous users
export async function checkIPRateLimit(ip: string): Promise<RateLimitResult> {
  const ipLimiter = new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(100, '1 h'), // 100 requests per hour per IP
    analytics: true,
    prefix: 'ratelimit:ip',
  });

  const result = await ipLimiter.limit(ip);
  
  return {
    success: result.success,
    limit: result.limit,
    remaining: result.remaining,
    reset: result.reset,
    retryAfter: result.success ? undefined : Math.ceil((result.reset.getTime() - Date.now()) / 1000),
  };
}

// Middleware helper for Next.js API routes
export function withRateLimit(
  action: 'enhancePrompt' | 'aiGeneration' | 'apiCalls',
  tier: 'free' | 'pro' | 'business' = 'free'
) {
  return async function rateLimitMiddleware(
    identifier: string,
    req: any,
    res: any,
    next: () => Promise<any>
  ) {
    const rateLimitService = RateLimitService.getInstance();
    const result = await rateLimitService.checkRateLimit(identifier, action, tier);

    // Add rate limit headers
    const headers = getRateLimitHeaders(result);
    Object.entries(headers).forEach(([key, value]) => {
      res.setHeader(key, value);
    });

    if (!result.success) {
      return res.status(429).json({
        error: 'Rate limit exceeded',
        message: `Too many requests. Try again in ${result.retryAfter} seconds.`,
        retryAfter: result.retryAfter,
      });
    }

    return next();
  };
}
