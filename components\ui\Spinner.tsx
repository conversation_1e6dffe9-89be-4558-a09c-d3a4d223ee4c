'use client';

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';
import { cn } from '@/utils/helpers';

const spinnerVariants = cva(
  'animate-spin',
  {
    variants: {
      size: {
        sm: 'h-4 w-4',
        default: 'h-6 w-6',
        lg: 'h-8 w-8',
        xl: 'h-12 w-12',
      },
      variant: {
        default: 'text-primary',
        secondary: 'text-secondary-foreground',
        muted: 'text-muted-foreground',
        white: 'text-white',
      },
    },
    defaultVariants: {
      size: 'default',
      variant: 'default',
    },
  }
);

export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  label?: string;
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size, variant, label, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('inline-flex items-center justify-center', className)}
        role="status"
        aria-label={label || 'Loading'}
        {...props}
      >
        <Loader2 className={cn(spinnerVariants({ size, variant }))} />
        {label && <span className="sr-only">{label}</span>}
      </div>
    );
  }
);
Spinner.displayName = 'Spinner';

// Loading Spinner with text
export interface LoadingSpinnerProps extends SpinnerProps {
  text?: string;
  textPosition?: 'right' | 'bottom';
}

const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ text, textPosition = 'right', className, size, variant, ...props }, ref) => {
    const isBottom = textPosition === 'bottom';

    return (
      <div
        ref={ref}
        className={cn(
          'inline-flex items-center',
          isBottom ? 'flex-col space-y-2' : 'space-x-2',
          className
        )}
        role="status"
        aria-label={text || 'Loading'}
        {...props}
      >
        <Loader2 className={cn(spinnerVariants({ size, variant }))} />
        {text && (
          <span className="text-sm text-muted-foreground">
            {text}
          </span>
        )}
      </div>
    );
  }
);
LoadingSpinner.displayName = 'LoadingSpinner';

// Page Loading Spinner
export interface PageSpinnerProps extends SpinnerProps {
  fullScreen?: boolean;
  backdrop?: boolean;
  message?: string;
}

const PageSpinner = React.forwardRef<HTMLDivElement, PageSpinnerProps>(
  ({ 
    fullScreen = false, 
    backdrop = false, 
    message = 'Loading...', 
    className, 
    size = 'xl',
    ...props 
  }, ref) => {
    const containerClasses = cn(
      'flex flex-col items-center justify-center space-y-4',
      fullScreen && 'fixed inset-0 z-50',
      !fullScreen && 'py-12',
      backdrop && 'bg-background/80 backdrop-blur-sm',
      className
    );

    return (
      <div ref={ref} className={containerClasses} {...props}>
        <Spinner size={size} />
        {message && (
          <p className="text-sm text-muted-foreground">{message}</p>
        )}
      </div>
    );
  }
);
PageSpinner.displayName = 'PageSpinner';

// Skeleton Loading Component
export interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  lines?: number;
  avatar?: boolean;
  width?: string | number;
  height?: string | number;
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ lines = 1, avatar = false, width, height, className, ...props }, ref) => {
    const skeletonLines = Array.from({ length: lines }, (_, i) => i);

    return (
      <div ref={ref} className={cn('animate-pulse', className)} {...props}>
        {avatar && (
          <div className="mb-4 h-12 w-12 rounded-full bg-muted" />
        )}
        {skeletonLines.map((line) => (
          <div
            key={line}
            className={cn(
              'mb-2 h-4 rounded bg-muted',
              line === lines - 1 && 'mb-0',
              line === lines - 1 && lines > 1 && 'w-3/4'
            )}
            style={{
              width: typeof width === 'number' ? `${width}px` : width,
              height: typeof height === 'number' ? `${height}px` : height,
            }}
          />
        ))}
      </div>
    );
  }
);
Skeleton.displayName = 'Skeleton';

// Card Skeleton
export interface CardSkeletonProps extends SkeletonProps {
  showImage?: boolean;
  showActions?: boolean;
}

const CardSkeleton = React.forwardRef<HTMLDivElement, CardSkeletonProps>(
  ({ showImage = false, showActions = false, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('rounded-lg border bg-card p-6', className)}
        {...props}
      >
        {showImage && (
          <div className="mb-4 h-48 w-full rounded bg-muted animate-pulse" />
        )}
        <div className="space-y-3">
          <div className="h-6 w-3/4 rounded bg-muted animate-pulse" />
          <div className="h-4 w-full rounded bg-muted animate-pulse" />
          <div className="h-4 w-5/6 rounded bg-muted animate-pulse" />
        </div>
        {showActions && (
          <div className="mt-6 flex space-x-2">
            <div className="h-9 w-20 rounded bg-muted animate-pulse" />
            <div className="h-9 w-16 rounded bg-muted animate-pulse" />
          </div>
        )}
      </div>
    );
  }
);
CardSkeleton.displayName = 'CardSkeleton';

// Table Skeleton
export interface TableSkeletonProps extends SkeletonProps {
  rows?: number;
  columns?: number;
}

const TableSkeleton = React.forwardRef<HTMLDivElement, TableSkeletonProps>(
  ({ rows = 5, columns = 4, className, ...props }, ref) => {
    const tableRows = Array.from({ length: rows }, (_, i) => i);
    const tableCols = Array.from({ length: columns }, (_, i) => i);

    return (
      <div ref={ref} className={cn('w-full', className)} {...props}>
        {/* Header */}
        <div className="mb-4 flex space-x-4">
          {tableCols.map((col) => (
            <div
              key={col}
              className="h-6 flex-1 rounded bg-muted animate-pulse"
            />
          ))}
        </div>
        
        {/* Rows */}
        <div className="space-y-3">
          {tableRows.map((row) => (
            <div key={row} className="flex space-x-4">
              {tableCols.map((col) => (
                <div
                  key={col}
                  className="h-4 flex-1 rounded bg-muted animate-pulse"
                />
              ))}
            </div>
          ))}
        </div>
      </div>
    );
  }
);
TableSkeleton.displayName = 'TableSkeleton';

// Dots Loading Animation
export interface DotsSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'primary' | 'secondary';
}

const DotsSpinner = React.forwardRef<HTMLDivElement, DotsSpinnerProps>(
  ({ size = 'default', variant = 'default', className, ...props }, ref) => {
    const sizeClasses = {
      sm: 'w-1 h-1',
      default: 'w-2 h-2',
      lg: 'w-3 h-3',
    };

    const variantClasses = {
      default: 'bg-muted-foreground',
      primary: 'bg-primary',
      secondary: 'bg-secondary-foreground',
    };

    return (
      <div
        ref={ref}
        className={cn('flex items-center space-x-1', className)}
        role="status"
        aria-label="Loading"
        {...props}
      >
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              'rounded-full animate-pulse',
              sizeClasses[size],
              variantClasses[variant]
            )}
            style={{
              animationDelay: `${i * 0.2}s`,
              animationDuration: '1s',
            }}
          />
        ))}
      </div>
    );
  }
);
DotsSpinner.displayName = 'DotsSpinner';

export {
  Spinner,
  LoadingSpinner,
  PageSpinner,
  Skeleton,
  CardSkeleton,
  TableSkeleton,
  DotsSpinner,
  spinnerVariants,
};
