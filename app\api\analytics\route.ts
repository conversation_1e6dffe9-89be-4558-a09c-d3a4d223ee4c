import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { supabase } from '@/lib/supabase';
import { DatabaseService } from '@/lib/supabase';
import { RateLimitService } from '@/lib/rateLimit';
import { AnalyticsService } from '@/lib/analytics';
import { validateForm } from '@/utils/validation';
import { getErrorMessage } from '@/utils/helpers';
import { ERROR_CODES } from '@/utils/constants';

const dbService = new DatabaseService();
const rateLimitService = new RateLimitService();
const analyticsService = new AnalyticsService();

// Validation schemas
const trackEventSchema = z.object({
  event: z.string().min(1).max(100),
  properties: z.record(z.any()).optional(),
  userId: z.string().optional(),
});

const getAnalyticsSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  metrics: z.array(z.string()).optional(),
  groupBy: z.enum(['day', 'week', 'month']).optional(),
});

// POST /api/analytics - Track events
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Rate limiting for analytics tracking
    const rateLimitResult = await rateLimitService.checkRateLimit(
      clientIP,
      'analytics_track',
      'free'
    );

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
          retryAfter: rateLimitResult.resetTime,
        },
        { status: 429 }
      );
    }

    // Validate request body
    const validation = validateForm(trackEventSchema, body);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          code: ERROR_CODES.VALIDATION_ERROR,
          details: validation.errors,
        },
        { status: 400 }
      );
    }

    const { event, properties, userId } = validation.data;

    // Get user from session if not provided
    let authenticatedUserId = userId;
    if (!authenticatedUserId) {
      const authHeader = request.headers.get('authorization');
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const { data: { user } } = await supabase.auth.getUser(token);
        authenticatedUserId = user?.id;
      }
    }

    // Track the event
    await analyticsService.trackEvent({
      event,
      userId: authenticatedUserId,
      properties: {
        ...properties,
        ip: clientIP,
        userAgent: request.headers.get('user-agent'),
        referer: request.headers.get('referer'),
      },
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      success: true,
      message: 'Event tracked successfully',
    });

  } catch (error) {
    console.error('Analytics tracking error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to track event',
        code: ERROR_CODES.INTERNAL_ERROR,
      },
      { status: 500 }
    );
  }
}

// GET /api/analytics - Get analytics data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    // Get user from session
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        {
          error: 'Authentication required',
          code: ERROR_CODES.AUTH_REQUIRED,
        },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        {
          error: 'Invalid authentication',
          code: ERROR_CODES.AUTH_INVALID,
        },
        { status: 401 }
      );
    }

    // Get user profile to check permissions
    const userProfile = await dbService.getUserProfile(user.id);
    
    // Check if user has analytics access
    const hasAnalyticsAccess = ['pro', 'business'].includes(userProfile.subscription_tier);
    if (!hasAnalyticsAccess) {
      return NextResponse.json(
        {
          error: 'Analytics access requires Pro or Business subscription',
          code: ERROR_CODES.SUBSCRIPTION_REQUIRED,
        },
        { status: 403 }
      );
    }

    // Rate limiting for analytics queries
    const rateLimitResult = await rateLimitService.checkRateLimit(
      user.id,
      'analytics_query',
      userProfile.subscription_tier
    );

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
          retryAfter: rateLimitResult.resetTime,
        },
        { status: 429 }
      );
    }

    // Validate query parameters
    const validation = validateForm(getAnalyticsSchema, queryParams);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          code: ERROR_CODES.VALIDATION_ERROR,
          details: validation.errors,
        },
        { status: 400 }
      );
    }

    const { startDate, endDate, metrics, groupBy } = validation.data;

    // Set default date range (last 30 days)
    const defaultEndDate = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 30);

    const queryStartDate = startDate ? new Date(startDate) : defaultStartDate;
    const queryEndDate = endDate ? new Date(endDate) : defaultEndDate;

    // Get analytics data
    const analyticsData = await getAnalyticsData(
      user.id,
      queryStartDate,
      queryEndDate,
      metrics,
      groupBy
    );

    return NextResponse.json({
      success: true,
      data: analyticsData,
      period: {
        startDate: queryStartDate.toISOString(),
        endDate: queryEndDate.toISOString(),
      },
    });

  } catch (error) {
    console.error('Analytics query error:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve analytics data',
        code: ERROR_CODES.INTERNAL_ERROR,
      },
      { status: 500 }
    );
  }
}

async function getAnalyticsData(
  userId: string,
  startDate: Date,
  endDate: Date,
  metrics?: string[],
  groupBy: 'day' | 'week' | 'month' = 'day'
) {
  try {
    // Base query for user's analytics data
    let query = supabase
      .from('analytics_events')
      .select('*')
      .eq('user_id', userId)
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString())
      .order('timestamp', { ascending: true });

    const { data: events, error } = await query;

    if (error) {
      throw error;
    }

    // Process analytics data
    const processedData = {
      overview: {
        totalEvents: events.length,
        uniqueDays: new Set(events.map(e => e.timestamp.split('T')[0])).size,
        topEvents: getTopEvents(events),
      },
      timeline: groupEventsByTime(events, groupBy),
      enhancements: getEnhancementMetrics(events),
      usage: getUsageMetrics(events),
    };

    // Filter by requested metrics if specified
    if (metrics && metrics.length > 0) {
      const filteredData: any = {};
      metrics.forEach(metric => {
        if (processedData[metric as keyof typeof processedData]) {
          filteredData[metric] = processedData[metric as keyof typeof processedData];
        }
      });
      return filteredData;
    }

    return processedData;

  } catch (error) {
    console.error('Error processing analytics data:', error);
    throw error;
  }
}

function getTopEvents(events: any[]) {
  const eventCounts: Record<string, number> = {};
  
  events.forEach(event => {
    eventCounts[event.event] = (eventCounts[event.event] || 0) + 1;
  });

  return Object.entries(eventCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([event, count]) => ({ event, count }));
}

function groupEventsByTime(events: any[], groupBy: 'day' | 'week' | 'month') {
  const grouped: Record<string, number> = {};

  events.forEach(event => {
    const date = new Date(event.timestamp);
    let key: string;

    switch (groupBy) {
      case 'day':
        key = date.toISOString().split('T')[0];
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
        break;
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
      default:
        key = date.toISOString().split('T')[0];
    }

    grouped[key] = (grouped[key] || 0) + 1;
  });

  return Object.entries(grouped)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([date, count]) => ({ date, count }));
}

function getEnhancementMetrics(events: any[]) {
  const enhancementEvents = events.filter(e => e.event === 'prompt_enhanced');
  
  const totalEnhancements = enhancementEvents.length;
  const avgQualityScore = enhancementEvents.reduce((sum, e) => 
    sum + (e.properties?.qualityScore || 0), 0) / totalEnhancements || 0;
  
  const providerUsage: Record<string, number> = {};
  enhancementEvents.forEach(e => {
    const provider = e.properties?.aiProvider || 'unknown';
    providerUsage[provider] = (providerUsage[provider] || 0) + 1;
  });

  return {
    totalEnhancements,
    avgQualityScore: Math.round(avgQualityScore * 100) / 100,
    providerUsage: Object.entries(providerUsage)
      .sort(([, a], [, b]) => b - a)
      .map(([provider, count]) => ({ provider, count })),
  };
}

function getUsageMetrics(events: any[]) {
  const dailyUsage: Record<string, number> = {};
  
  events.forEach(event => {
    const date = event.timestamp.split('T')[0];
    dailyUsage[date] = (dailyUsage[date] || 0) + 1;
  });

  const usageDays = Object.keys(dailyUsage).length;
  const totalEvents = events.length;
  const avgDailyUsage = usageDays > 0 ? totalEvents / usageDays : 0;

  return {
    totalEvents,
    activeDays: usageDays,
    avgDailyUsage: Math.round(avgDailyUsage * 100) / 100,
    peakUsageDay: Object.entries(dailyUsage)
      .sort(([, a], [, b]) => b - a)[0] || null,
  };
}
