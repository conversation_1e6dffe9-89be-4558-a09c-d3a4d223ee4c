{"name": "ai-prompt-enhancer", "version": "1.0.0", "description": "Transform your AI prompts into powerful, detailed instructions", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "build:netlify": "next build && next export", "build:aws": "next build && serverless package", "build:gcp": "next build && npm run export", "export": "next export", "analyze": "cross-env ANALYZE=true next build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["ai", "prompt", "enhancement", "chatgpt", "claude", "gemini", "openai", "anthropic", "nextjs", "typescript", "serverless"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/HectorTa1989"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/ai-prompt-enhancer.git"}, "bugs": {"url": "https://github.com/HectorTa1989/ai-prompt-enhancer/issues"}, "homepage": "https://github.com/HectorTa1989/ai-prompt-enhancer#readme", "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@supabase/supabase-js": "^2.38.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "@upstash/redis": "^1.25.1", "@upstash/ratelimit": "^0.4.4", "axios": "^1.6.2", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2", "tailwind-merge": "^2.1.0", "class-variance-authority": "^0.7.0", "next-themes": "^0.2.1", "react-intersection-observer": "^9.5.3", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "postcss": "^8.4.32", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.4", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "jest-environment-jsdom": "^29.7.0", "serverless": "^3.38.0", "serverless-nextjs-plugin": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}