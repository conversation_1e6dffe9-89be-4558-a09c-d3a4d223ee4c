# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# AI Provider API Keys (Optional - for direct API calls)
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
GOOGLE_AI_API_KEY=your-google-ai-api-key
HUGGINGFACE_API_KEY=hf_your-huggingface-api-key

# Rate Limiting (Upstash Redis)
UPSTASH_REDIS_REST_URL=https://your-redis.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID=GTM-XXXXXXX

# SEO & Social
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_SITE_NAME=AI Prompt Enhancer
NEXT_PUBLIC_SITE_DESCRIPTION=Transform your AI prompts into powerful, detailed instructions
NEXT_PUBLIC_TWITTER_HANDLE=@yourtwitterhandle
NEXT_PUBLIC_FACEBOOK_APP_ID=your-facebook-app-id

# Security
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://your-domain.com

# Database (if using external PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/ai_prompt_enhancer

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Stripe (for payments)
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Deployment Environment
NODE_ENV=development
BUILD_TARGET=vercel

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_PAYMENTS=false
NEXT_PUBLIC_ENABLE_SOCIAL_LOGIN=true

# Rate Limiting Configuration
RATE_LIMIT_FREE_TIER=50
RATE_LIMIT_PRO_TIER=500
RATE_LIMIT_BUSINESS_TIER=5000

# Cache Configuration
CACHE_TTL_SECONDS=3600
REDIS_CACHE_PREFIX=ai-prompt-enhancer:

# Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Performance
ENABLE_COMPRESSION=true
ENABLE_STATIC_OPTIMIZATION=true

# Security Headers
ENABLE_CSP=true
ENABLE_HSTS=true
ENABLE_FRAME_OPTIONS=true
