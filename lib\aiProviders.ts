import axios, { AxiosResponse } from 'axios';

export interface AIProvider {
  id: string;
  name: string;
  description: string;
  endpoint: string;
  headers: (apiKey: string) => Record<string, string>;
  formatRequest: (prompt: string, options?: AIRequestOptions) => any;
  parseResponse: (response: any) => AIResponse;
  costPerToken: number;
  maxTokens: number;
  supportsStreaming: boolean;
  rateLimit: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

export interface AIRequestOptions {
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  model?: string;
  stream?: boolean;
}

export interface AIResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: string;
  metadata?: Record<string, any>;
}

export interface AIProviderError {
  code: string;
  message: string;
  type: 'rate_limit' | 'invalid_request' | 'authentication' | 'server_error' | 'unknown';
  retryAfter?: number;
}

// Free AI Providers Configuration
export const aiProviders: Record<string, AIProvider> = {
  huggingface: {
    id: 'huggingface',
    name: 'Hugging Face',
    description: 'Free open-source models via Hugging Face Inference API',
    endpoint: 'https://api-inference.huggingface.co/models',
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    }),
    formatRequest: (prompt: string, options = {}) => ({
      inputs: prompt,
      parameters: {
        max_new_tokens: options.maxTokens || 512,
        temperature: options.temperature || 0.7,
        top_p: options.topP || 0.9,
        do_sample: true,
        return_full_text: false,
      },
      options: {
        wait_for_model: true,
        use_cache: false,
      },
    }),
    parseResponse: (response: any): AIResponse => ({
      content: response[0]?.generated_text || response.generated_text || '',
      usage: {
        promptTokens: Math.ceil((response.inputs?.length || 0) / 4),
        completionTokens: Math.ceil((response[0]?.generated_text?.length || 0) / 4),
        totalTokens: Math.ceil(((response.inputs?.length || 0) + (response[0]?.generated_text?.length || 0)) / 4),
      },
      model: 'microsoft/DialoGPT-large',
      finishReason: 'stop',
    }),
    costPerToken: 0, // Free tier
    maxTokens: 2048,
    supportsStreaming: false,
    rateLimit: {
      requestsPerMinute: 30,
      tokensPerMinute: 10000,
    },
  },
  
  openai: {
    id: 'openai',
    name: 'OpenAI GPT',
    description: 'OpenAI GPT models (requires API key)',
    endpoint: 'https://api.openai.com/v1/chat/completions',
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    }),
    formatRequest: (prompt: string, options = {}) => ({
      model: options.model || 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: options.maxTokens || 1000,
      temperature: options.temperature || 0.7,
      top_p: options.topP || 1,
      stream: options.stream || false,
    }),
    parseResponse: (response: any): AIResponse => ({
      content: response.choices[0]?.message?.content || '',
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0,
      },
      model: response.model,
      finishReason: response.choices[0]?.finish_reason || 'stop',
    }),
    costPerToken: 0.002,
    maxTokens: 4096,
    supportsStreaming: true,
    rateLimit: {
      requestsPerMinute: 60,
      tokensPerMinute: 90000,
    },
  },

  anthropic: {
    id: 'anthropic',
    name: 'Anthropic Claude',
    description: 'Anthropic Claude models (requires API key)',
    endpoint: 'https://api.anthropic.com/v1/messages',
    headers: (apiKey: string) => ({
      'x-api-key': apiKey,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01',
    }),
    formatRequest: (prompt: string, options = {}) => ({
      model: options.model || 'claude-3-sonnet-20240229',
      max_tokens: options.maxTokens || 1000,
      messages: [{ role: 'user', content: prompt }],
      temperature: options.temperature || 0.7,
      top_p: options.topP || 1,
    }),
    parseResponse: (response: any): AIResponse => ({
      content: response.content[0]?.text || '',
      usage: {
        promptTokens: response.usage?.input_tokens || 0,
        completionTokens: response.usage?.output_tokens || 0,
        totalTokens: (response.usage?.input_tokens || 0) + (response.usage?.output_tokens || 0),
      },
      model: response.model,
      finishReason: response.stop_reason || 'stop',
    }),
    costPerToken: 0.003,
    maxTokens: 4096,
    supportsStreaming: true,
    rateLimit: {
      requestsPerMinute: 50,
      tokensPerMinute: 40000,
    },
  },

  google: {
    id: 'google',
    name: 'Google Gemini',
    description: 'Google Gemini models (requires API key)',
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
    headers: (apiKey: string) => ({
      'Content-Type': 'application/json',
    }),
    formatRequest: (prompt: string, options = {}) => ({
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        maxOutputTokens: options.maxTokens || 1000,
        temperature: options.temperature || 0.7,
        topP: options.topP || 0.8,
      },
    }),
    parseResponse: (response: any): AIResponse => {
      const content = response.candidates?.[0]?.content?.parts?.[0]?.text || '';
      return {
        content,
        usage: {
          promptTokens: response.usageMetadata?.promptTokenCount || 0,
          completionTokens: response.usageMetadata?.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata?.totalTokenCount || 0,
        },
        model: 'gemini-pro',
        finishReason: response.candidates?.[0]?.finishReason || 'STOP',
      };
    },
    costPerToken: 0.001,
    maxTokens: 2048,
    supportsStreaming: false,
    rateLimit: {
      requestsPerMinute: 60,
      tokensPerMinute: 32000,
    },
  },
};

// AI Provider Service Class
export class AIProviderService {
  private provider: AIProvider;
  private apiKey: string;

  constructor(providerId: string, apiKey: string) {
    this.provider = aiProviders[providerId];
    if (!this.provider) {
      throw new Error(`Unknown AI provider: ${providerId}`);
    }
    this.apiKey = apiKey;
  }

  async generateResponse(
    prompt: string,
    options?: AIRequestOptions
  ): Promise<AIResponse> {
    try {
      const requestData = this.provider.formatRequest(prompt, options);
      const headers = this.provider.headers(this.apiKey);
      
      // Add API key to URL for Google
      const url = this.provider.id === 'google' 
        ? `${this.provider.endpoint}?key=${this.apiKey}`
        : this.provider.endpoint;

      // For Hugging Face, append model to endpoint
      const finalUrl = this.provider.id === 'huggingface'
        ? `${this.provider.endpoint}/microsoft/DialoGPT-large`
        : url;

      const response: AxiosResponse = await axios.post(finalUrl, requestData, {
        headers,
        timeout: 30000,
      });

      return this.provider.parseResponse(response.data);
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  private handleError(error: any): AIProviderError {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      switch (status) {
        case 429:
          return {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Rate limit exceeded. Please try again later.',
            type: 'rate_limit',
            retryAfter: parseInt(error.response.headers['retry-after']) || 60,
          };
        case 401:
          return {
            code: 'INVALID_API_KEY',
            message: 'Invalid API key provided.',
            type: 'authentication',
          };
        case 400:
          return {
            code: 'INVALID_REQUEST',
            message: data.error?.message || 'Invalid request parameters.',
            type: 'invalid_request',
          };
        case 500:
        case 502:
        case 503:
          return {
            code: 'SERVER_ERROR',
            message: 'AI provider server error. Please try again.',
            type: 'server_error',
          };
        default:
          return {
            code: 'UNKNOWN_ERROR',
            message: data.error?.message || 'Unknown error occurred.',
            type: 'unknown',
          };
      }
    }

    return {
      code: 'NETWORK_ERROR',
      message: 'Network error. Please check your connection.',
      type: 'unknown',
    };
  }

  getProvider(): AIProvider {
    return this.provider;
  }

  estimateCost(tokens: number): number {
    return tokens * this.provider.costPerToken;
  }
}

// Utility functions
export function getAvailableProviders(): AIProvider[] {
  return Object.values(aiProviders);
}

export function getProviderById(id: string): AIProvider | undefined {
  return aiProviders[id];
}

export function getFreeProviders(): AIProvider[] {
  return Object.values(aiProviders).filter(provider => provider.costPerToken === 0);
}

export function validateApiKey(providerId: string, apiKey: string): boolean {
  if (!apiKey || apiKey.trim() === '') return false;
  
  switch (providerId) {
    case 'openai':
      return apiKey.startsWith('sk-');
    case 'anthropic':
      return apiKey.startsWith('sk-ant-');
    case 'huggingface':
      return apiKey.startsWith('hf_');
    case 'google':
      return apiKey.length > 20; // Basic length check
    default:
      return true;
  }
}
