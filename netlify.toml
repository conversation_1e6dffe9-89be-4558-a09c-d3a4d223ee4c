[build]
  publish = ".next"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"
  NEXT_TELEMETRY_DISABLED = "1"

# Production context
[context.production]
  environment = { NODE_ENV = "production" }

# Deploy preview context
[context.deploy-preview]
  environment = { NODE_ENV = "preview" }

# Branch deploy context
[context.branch-deploy]
  environment = { NODE_ENV = "development" }

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"
    Strict-Transport-Security = "max-age=********; includeSubDomains; preload"

# API CORS headers
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With"
    Access-Control-Max-Age = "86400"

# Static asset caching
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.png"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.svg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Redirects
[[redirects]]
  from = "/docs"
  to = "/api-docs"
  status = 301

[[redirects]]
  from = "/github"
  to = "https://github.com/ai-prompt-enhancer/ai-prompt-enhancer"
  status = 302

[[redirects]]
  from = "/discord"
  to = "https://discord.gg/ai-prompt-enhancer"
  status = 302

# API rewrites
[[redirects]]
  from = "/sitemap.xml"
  to = "/api/sitemap"
  status = 200

[[redirects]]
  from = "/robots.txt"
  to = "/api/robots"
  status = 200

# SPA fallback for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# Functions configuration
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Edge functions
[edge_functions]
  directory = "netlify/edge-functions"

# Plugin configuration
[[plugins]]
  package = "@netlify/plugin-nextjs"

[[plugins]]
  package = "netlify-plugin-cache"
  [plugins.inputs]
    paths = [
      ".next/cache",
      "node_modules/.cache"
    ]

[[plugins]]
  package = "@netlify/plugin-lighthouse"
  [plugins.inputs]
    output_path = "reports/lighthouse"
    audit_url = "/"

# Environment variables (these should be set in Netlify dashboard)
# NEXT_PUBLIC_SITE_URL
# NEXT_PUBLIC_SUPABASE_URL
# NEXT_PUBLIC_SUPABASE_ANON_KEY
# SUPABASE_SERVICE_ROLE_KEY
# UPSTASH_REDIS_REST_URL
# UPSTASH_REDIS_REST_TOKEN
# OPENAI_API_KEY
# ANTHROPIC_API_KEY
# GOOGLE_AI_API_KEY
# HUGGINGFACE_API_KEY
# GOOGLE_ANALYTICS_ID
# RESEND_API_KEY

# Form handling
[forms]
  settings = { spam_protection = true }

# Large Media
[large_media]
  git_lfs = true

# Split testing
[split_testing]
  [split_testing.test_1]
    path = "/"
    branches = ["main", "feature/new-homepage"]

# Dev server configuration
[dev]
  command = "npm run dev"
  port = 3000
  publish = ".next"
  autoLaunch = false

# Build processing
[processing]
  skip_processing = false

[processing.css]
  bundle = true
  minify = true

[processing.js]
  bundle = true
  minify = true

[processing.html]
  pretty_urls = true

[processing.images]
  compress = true
