// Global type definitions for the AI Prompt Enhancer application

// User and Authentication Types
export interface User {
  id: string;
  email: string;
  created_at: string;
  subscription_tier: SubscriptionTier;
  monthly_usage: number;
  last_reset_date: string;
  profile?: UserProfile;
}

export interface UserProfile {
  id: string;
  user_id: string;
  full_name?: string;
  avatar_url?: string;
  website?: string;
  bio?: string;
  preferences: UserPreferences;
  created_at: string;
  updated_at: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  defaultAiProvider: string;
  enhancementLevel: 'basic' | 'advanced' | 'expert';
  emailNotifications: boolean;
  analyticsEnabled: boolean;
}

export type SubscriptionTier = 'free' | 'pro' | 'business';

// Prompt Enhancement Types
export interface PromptEnhancement {
  id: string;
  user_id: string;
  original_prompt: string;
  enhanced_prompt: string;
  ai_provider?: string;
  response_quality_score?: number;
  applied_rules: string[];
  estimated_tokens: number;
  created_at: string;
}

export interface EnhancementRequest {
  prompt: string;
  context?: {
    userLevel?: 'beginner' | 'intermediate' | 'advanced';
    domain?: string;
    targetAI?: string;
  };
  options?: {
    enhancementLevel?: 'basic' | 'advanced' | 'expert';
    includeExamples?: boolean;
    maxLength?: number;
  };
}

export interface EnhancementResponse {
  originalPrompt: string;
  enhancedPrompt: string;
  appliedRules: string[];
  qualityScore: number;
  suggestions: string[];
  estimatedTokens: number;
  processingTime: number;
}

// AI Provider Types
export interface AIProvider {
  id: string;
  name: string;
  description: string;
  endpoint: string;
  costPerToken: number;
  maxTokens: number;
  supportsStreaming: boolean;
  rateLimit: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

export interface AIRequest {
  prompt: string;
  provider: string;
  options?: {
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    model?: string;
    stream?: boolean;
  };
}

export interface AIResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: string;
  metadata?: Record<string, any>;
}

// Template Types
export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  template_text: string;
  variables: string[];
  usage_count: number;
  is_public: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  templateCount: number;
}

// Analytics Types
export interface UsageAnalytics {
  id: string;
  user_id: string;
  action: string;
  metadata: Record<string, any>;
  created_at: string;
}

export interface AnalyticsData {
  totalEnhancements: number;
  totalAIGenerations: number;
  averageQualityScore: number;
  mostUsedProviders: Array<{ provider: string; count: number }>;
  dailyUsage: Array<{ date: string; count: number }>;
  monthlyUsage: Array<{ month: string; count: number }>;
}

// Rate Limiting Types
export interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}

export interface RateLimitConfig {
  requests: number;
  window: string;
  identifier: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    rateLimit?: RateLimitResult;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filter?: Record<string, any>;
}

// UI Component Types
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  type?: 'text' | 'email' | 'password' | 'number' | 'url';
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  fullName?: string;
  agreeToTerms: boolean;
}

export interface ProfileForm {
  fullName?: string;
  bio?: string;
  website?: string;
  preferences: UserPreferences;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  statusCode?: number;
  details?: any;
  timestamp: string;
}

export type ErrorCode = 
  | 'VALIDATION_ERROR'
  | 'AUTHENTICATION_ERROR'
  | 'AUTHORIZATION_ERROR'
  | 'RATE_LIMIT_ERROR'
  | 'PROVIDER_ERROR'
  | 'DATABASE_ERROR'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';

// Notification Types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

// Search and Filter Types
export interface SearchParams {
  query?: string;
  category?: string;
  provider?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  qualityScore?: {
    min: number;
    max: number;
  };
}

export interface FilterOption {
  label: string;
  value: string;
  count?: number;
}

// Dashboard Types
export interface DashboardStats {
  totalEnhancements: number;
  monthlyUsage: number;
  remainingQuota: number;
  averageQualityScore: number;
  recentEnhancements: PromptEnhancement[];
  usageChart: Array<{ date: string; count: number }>;
}

// Settings Types
export interface AppSettings {
  general: {
    siteName: string;
    siteUrl: string;
    supportEmail: string;
  };
  features: {
    enableAnalytics: boolean;
    enablePayments: boolean;
    enableSocialLogin: boolean;
  };
  limits: {
    freeEnhancements: number;
    proEnhancements: number;
    businessEnhancements: number;
  };
}

// Webhook Types
export interface WebhookEvent {
  id: string;
  type: string;
  data: any;
  timestamp: string;
  signature: string;
}

// Export utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Environment Types
export interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test';
  NEXT_PUBLIC_SUPABASE_URL: string;
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
  UPSTASH_REDIS_REST_URL: string;
  UPSTASH_REDIS_REST_TOKEN: string;
  OPENAI_API_KEY?: string;
  ANTHROPIC_API_KEY?: string;
  GOOGLE_AI_API_KEY?: string;
  HUGGINGFACE_API_KEY?: string;
  NEXT_PUBLIC_GOOGLE_ANALYTICS_ID?: string;
  NEXT_PUBLIC_SITE_URL: string;
}
