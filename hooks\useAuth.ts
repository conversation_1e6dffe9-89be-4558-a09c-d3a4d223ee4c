'use client';

import { useState, useEffect, useContext, createContext, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { User, Session, AuthError } from '@supabase/supabase-js';

import { supabase } from '@/lib/supabase';
import { DatabaseService } from '@/lib/supabase';
import { UserProfile } from '@/types';

interface AuthContextType {
  user: UserProfile | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: AuthError }>;
  signUp: (email: string, password: string, metadata?: Record<string, any>) => Promise<{ error?: AuthError }>;
  signOut: () => Promise<{ error?: AuthError }>;
  resetPassword: (email: string) => Promise<{ error?: AuthError }>;
  updatePassword: (password: string) => Promise<{ error?: AuthError }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error?: Error }>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const dbService = new DatabaseService();

  // Initialize auth state
  useEffect(() => {
    let mounted = true;

    async function getInitialSession() {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
          return;
        }

        if (mounted) {
          setSession(session);
          
          if (session?.user) {
            await loadUserProfile(session.user.id);
          } else {
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    }

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        console.log('Auth state changed:', event, session?.user?.id);
        
        setSession(session);
        
        if (session?.user) {
          await loadUserProfile(session.user.id);
        } else {
          setUser(null);
        }
        
        setLoading(false);

        // Handle specific auth events
        if (event === 'SIGNED_OUT') {
          router.push('/');
        } else if (event === 'SIGNED_IN') {
          // Redirect to dashboard or intended page
          const redirectTo = sessionStorage.getItem('redirectAfterAuth');
          if (redirectTo) {
            sessionStorage.removeItem('redirectAfterAuth');
            router.push(redirectTo);
          } else {
            router.push('/dashboard');
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [router]);

  // Load user profile from database
  async function loadUserProfile(userId: string) {
    try {
      const profile = await dbService.getUserProfile(userId);
      setUser(profile);
    } catch (error) {
      console.error('Error loading user profile:', error);
      // Create a basic profile if none exists
      try {
        const { data: { user: authUser } } = await supabase.auth.getUser();
        if (authUser) {
          const basicProfile: Partial<UserProfile> = {
            id: authUser.id,
            email: authUser.email!,
            subscription_tier: 'free',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
          
          const createdProfile = await dbService.createUserProfile(basicProfile);
          setUser(createdProfile);
        }
      } catch (createError) {
        console.error('Error creating user profile:', createError);
        setUser(null);
      }
    }
  }

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      return { error: error || undefined };
    } catch (error) {
      console.error('Sign in error:', error);
      return { error: error as AuthError };
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, metadata?: Record<string, any>) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });
      
      return { error: error || undefined };
    } catch (error) {
      console.error('Sign up error:', error);
      return { error: error as AuthError };
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      
      // Clear local state immediately
      setUser(null);
      setSession(null);
      
      return { error: error || undefined };
    } catch (error) {
      console.error('Sign out error:', error);
      return { error: error as AuthError };
    } finally {
      setLoading(false);
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      
      return { error: error || undefined };
    } catch (error) {
      console.error('Reset password error:', error);
      return { error: error as AuthError };
    }
  };

  // Update password
  const updatePassword = async (password: string) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password,
      });
      
      return { error: error || undefined };
    } catch (error) {
      console.error('Update password error:', error);
      return { error: error as AuthError };
    }
  };

  // Update user profile
  const updateProfile = async (updates: Partial<UserProfile>) => {
    try {
      if (!user) {
        throw new Error('No user logged in');
      }

      const updatedProfile = await dbService.updateUserProfile(user.id, updates);
      setUser(updatedProfile);
      
      return {};
    } catch (error) {
      console.error('Update profile error:', error);
      return { error: error as Error };
    }
  };

  // Refresh user data
  const refreshUser = async () => {
    if (session?.user) {
      await loadUserProfile(session.user.id);
    }
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}

// Hook for protected routes
export function useRequireAuth(redirectTo: string = '/login') {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      // Store the current path for redirect after login
      sessionStorage.setItem('redirectAfterAuth', window.location.pathname);
      router.push(redirectTo);
    }
  }, [user, loading, router, redirectTo]);

  return { user, loading };
}

// Hook for guest-only routes (redirect if authenticated)
export function useGuestOnly(redirectTo: string = '/dashboard') {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      router.push(redirectTo);
    }
  }, [user, loading, router, redirectTo]);

  return { user, loading };
}

// Hook to check subscription tier
export function useSubscription() {
  const { user } = useAuth();
  
  const tier = user?.subscription_tier || 'free';
  const isFreeTier = tier === 'free';
  const isProTier = tier === 'pro';
  const isBusinessTier = tier === 'business';
  const isPaidTier = tier !== 'free';

  return {
    tier,
    isFreeTier,
    isProTier,
    isBusinessTier,
    isPaidTier,
    user,
  };
}

// Hook for role-based access control
export function usePermissions() {
  const { user } = useAuth();
  
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Define permissions based on subscription tier
    const permissions = {
      free: ['basic_enhancement', 'view_templates'],
      pro: ['basic_enhancement', 'advanced_enhancement', 'view_templates', 'create_templates', 'analytics'],
      business: ['basic_enhancement', 'advanced_enhancement', 'view_templates', 'create_templates', 'analytics', 'api_access', 'priority_support'],
    };
    
    const userPermissions = permissions[user.subscription_tier as keyof typeof permissions] || permissions.free;
    return userPermissions.includes(permission);
  };

  const canUseFeature = (feature: string): boolean => {
    const featurePermissions = {
      'advanced_enhancement': ['pro', 'business'],
      'api_access': ['business'],
      'priority_support': ['business'],
      'analytics': ['pro', 'business'],
      'custom_templates': ['pro', 'business'],
    };
    
    const requiredTiers = featurePermissions[feature as keyof typeof featurePermissions];
    return requiredTiers ? requiredTiers.includes(user?.subscription_tier || 'free') : true;
  };

  return {
    hasPermission,
    canUseFeature,
    user,
  };
}
