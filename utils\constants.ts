// Application constants and configuration

// Subscription tiers and limits
export const SUBSCRIPTION_TIERS = {
  FREE: 'free',
  PRO: 'pro',
  BUSINESS: 'business',
} as const;

export const SUBSCRIPTION_LIMITS = {
  [SUBSCRIPTION_TIERS.FREE]: {
    enhancementsPerDay: 50,
    aiGenerationsPerDay: 10,
    templatesLimit: 5,
    analyticsRetention: 7, // days
    supportLevel: 'community',
  },
  [SUBSCRIPTION_TIERS.PRO]: {
    enhancementsPerDay: 500,
    aiGenerationsPerDay: 100,
    templatesLimit: 50,
    analyticsRetention: 30, // days
    supportLevel: 'email',
  },
  [SUBSCRIPTION_TIERS.BUSINESS]: {
    enhancementsPerDay: 5000,
    aiGenerationsPerDay: 1000,
    templatesLimit: -1, // unlimited
    analyticsRetention: 365, // days
    supportLevel: 'priority',
  },
} as const;

// AI Provider configurations
export const AI_PROVIDERS = {
  HUGGINGFACE: 'huggingface',
  OPENAI: 'openai',
  ANTHROPIC: 'anthropic',
  GOOGLE: 'google',
} as const;

export const AI_PROVIDER_MODELS = {
  [AI_PROVIDERS.HUGGINGFACE]: [
    'microsoft/DialoGPT-large',
    'facebook/blenderbot-400M-distill',
    'microsoft/DialoGPT-medium',
  ],
  [AI_PROVIDERS.OPENAI]: [
    'gpt-3.5-turbo',
    'gpt-3.5-turbo-16k',
    'gpt-4',
    'gpt-4-turbo-preview',
  ],
  [AI_PROVIDERS.ANTHROPIC]: [
    'claude-3-sonnet-20240229',
    'claude-3-opus-20240229',
    'claude-3-haiku-20240307',
  ],
  [AI_PROVIDERS.GOOGLE]: [
    'gemini-pro',
    'gemini-pro-vision',
  ],
} as const;

// Enhancement categories
export const ENHANCEMENT_CATEGORIES = {
  CLARITY: 'clarity',
  SPECIFICITY: 'specificity',
  STRUCTURE: 'structure',
  CONTEXT: 'context',
  EXAMPLES: 'examples',
  CONSTRAINTS: 'constraints',
} as const;

// Template categories
export const TEMPLATE_CATEGORIES = {
  WRITING: 'writing',
  CODING: 'coding',
  ANALYSIS: 'analysis',
  CREATIVE: 'creative',
  BUSINESS: 'business',
  EDUCATION: 'education',
  RESEARCH: 'research',
  MARKETING: 'marketing',
} as const;

export const TEMPLATE_CATEGORY_INFO = {
  [TEMPLATE_CATEGORIES.WRITING]: {
    name: 'Writing',
    description: 'Templates for content creation, copywriting, and documentation',
    icon: '✍️',
    color: 'blue',
  },
  [TEMPLATE_CATEGORIES.CODING]: {
    name: 'Coding',
    description: 'Programming, debugging, and technical documentation templates',
    icon: '💻',
    color: 'green',
  },
  [TEMPLATE_CATEGORIES.ANALYSIS]: {
    name: 'Analysis',
    description: 'Data analysis, research, and evaluation templates',
    icon: '📊',
    color: 'purple',
  },
  [TEMPLATE_CATEGORIES.CREATIVE]: {
    name: 'Creative',
    description: 'Creative writing, brainstorming, and ideation templates',
    icon: '🎨',
    color: 'pink',
  },
  [TEMPLATE_CATEGORIES.BUSINESS]: {
    name: 'Business',
    description: 'Business strategy, planning, and communication templates',
    icon: '💼',
    color: 'indigo',
  },
  [TEMPLATE_CATEGORIES.EDUCATION]: {
    name: 'Education',
    description: 'Learning, teaching, and educational content templates',
    icon: '🎓',
    color: 'yellow',
  },
  [TEMPLATE_CATEGORIES.RESEARCH]: {
    name: 'Research',
    description: 'Academic research, literature review, and study templates',
    icon: '🔬',
    color: 'teal',
  },
  [TEMPLATE_CATEGORIES.MARKETING]: {
    name: 'Marketing',
    description: 'Marketing campaigns, social media, and promotional templates',
    icon: '📢',
    color: 'orange',
  },
} as const;

// Quality score thresholds
export const QUALITY_THRESHOLDS = {
  EXCELLENT: 90,
  GOOD: 75,
  FAIR: 60,
  POOR: 40,
} as const;

// Rate limiting windows
export const RATE_LIMIT_WINDOWS = {
  MINUTE: '1 m',
  HOUR: '1 h',
  DAY: '1 d',
  WEEK: '7 d',
  MONTH: '30 d',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  ENHANCE: '/api/enhance',
  AI_GENERATE: '/api/ai/generate',
  TEMPLATES: '/api/templates',
  ANALYTICS: '/api/analytics',
  USER: '/api/user',
  AUTH: '/api/auth',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'ai-prompt-enhancer-theme',
  USER_PREFERENCES: 'ai-prompt-enhancer-preferences',
  RECENT_PROMPTS: 'ai-prompt-enhancer-recent-prompts',
  DRAFT_PROMPT: 'ai-prompt-enhancer-draft',
} as const;

// Error codes
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  PROVIDER_ERROR: 'PROVIDER_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Validation rules
export const VALIDATION_RULES = {
  PROMPT: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 10000,
  },
  TEMPLATE: {
    NAME_MIN_LENGTH: 3,
    NAME_MAX_LENGTH: 100,
    DESCRIPTION_MAX_LENGTH: 500,
    TEMPLATE_MAX_LENGTH: 5000,
  },
  USER: {
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PASSWORD_MIN_LENGTH: 8,
    NAME_MAX_LENGTH: 100,
    BIO_MAX_LENGTH: 500,
  },
} as const;

// UI constants
export const UI_CONSTANTS = {
  ANIMATION_DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536,
  },
  Z_INDEX: {
    DROPDOWN: 1000,
    STICKY: 1020,
    FIXED: 1030,
    MODAL_BACKDROP: 1040,
    MODAL: 1050,
    POPOVER: 1060,
    TOOLTIP: 1070,
    TOAST: 1080,
  },
} as const;

// Date formats
export const DATE_FORMATS = {
  SHORT: 'MMM d, yyyy',
  LONG: 'MMMM d, yyyy',
  WITH_TIME: 'MMM d, yyyy h:mm a',
  ISO: 'yyyy-MM-dd',
  TIME_ONLY: 'h:mm a',
} as const;

// Feature flags
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
  ENABLE_PAYMENTS: process.env.NEXT_PUBLIC_ENABLE_PAYMENTS === 'true',
  ENABLE_SOCIAL_LOGIN: process.env.NEXT_PUBLIC_ENABLE_SOCIAL_LOGIN === 'true',
  ENABLE_DARK_MODE: true,
  ENABLE_TEMPLATES: true,
  ENABLE_AI_GENERATION: true,
} as const;

// Social media links
export const SOCIAL_LINKS = {
  TWITTER: 'https://twitter.com/hectorta1989',
  GITHUB: 'https://github.com/HectorTa1989',
  LINKEDIN: 'https://linkedin.com/in/hectorta1989',
} as const;

// External links
export const EXTERNAL_LINKS = {
  DOCUMENTATION: 'https://docs.ai-prompt-enhancer.com',
  SUPPORT: 'https://support.ai-prompt-enhancer.com',
  BLOG: 'https://blog.ai-prompt-enhancer.com',
  CHANGELOG: 'https://changelog.ai-prompt-enhancer.com',
  STATUS: 'https://status.ai-prompt-enhancer.com',
} as const;

// Regex patterns
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
} as const;

// File upload constraints
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.webp', '.gif'],
} as const;

// Pagination defaults
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

// Cache durations (in seconds)
export const CACHE_DURATION = {
  SHORT: 300, // 5 minutes
  MEDIUM: 1800, // 30 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86400, // 24 hours
} as const;

// Environment-specific constants
export const IS_PRODUCTION = process.env.NODE_ENV === 'production';
export const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';
export const IS_TEST = process.env.NODE_ENV === 'test';

// App metadata
export const APP_METADATA = {
  NAME: 'AI Prompt Enhancer',
  DESCRIPTION: 'Transform your AI prompts into powerful, detailed instructions',
  VERSION: '1.0.0',
  AUTHOR: 'Hector Ta',
  KEYWORDS: [
    'AI',
    'prompt',
    'enhancement',
    'ChatGPT',
    'Claude',
    'Gemini',
    'optimization',
  ],
} as const;
