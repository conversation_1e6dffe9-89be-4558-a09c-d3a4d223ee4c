import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { supabase } from '@/lib/supabase';
import { DatabaseService } from '@/lib/supabase';
import { RateLimitService } from '@/lib/rateLimit';
import { AnalyticsService } from '@/lib/analytics';
import { validateForm, loginSchema, registerSchema, forgotPasswordSchema } from '@/utils/validation';
import { getErrorMessage } from '@/utils/helpers';
import { ERROR_CODES } from '@/utils/constants';

const dbService = new DatabaseService();
const rateLimitService = new RateLimitService();
const analyticsService = new AnalyticsService();

// POST /api/auth - Handle authentication requests
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    switch (action) {
      case 'login':
        return await handleLogin(body, clientIP);
      case 'register':
        return await handleRegister(body, clientIP);
      case 'forgot-password':
        return await handleForgotPassword(body, clientIP);
      case 'logout':
        return await handleLogout();
      default:
        return NextResponse.json(
          { 
            error: 'Invalid action',
            code: ERROR_CODES.VALIDATION_ERROR 
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Auth API error:', error);
    
    // Track error
    await analyticsService.trackError({
      error: getErrorMessage(error),
      context: 'auth_api',
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      { 
        error: 'Internal server error',
        code: ERROR_CODES.INTERNAL_ERROR 
      },
      { status: 500 }
    );
  }
}

async function handleLogin(body: any, clientIP: string) {
  // Rate limiting for login attempts
  const rateLimitResult = await rateLimitService.checkRateLimit(
    clientIP,
    'auth_login',
    'free'
  );

  if (!rateLimitResult.allowed) {
    await analyticsService.trackEvent({
      event: 'auth_rate_limited',
      properties: {
        action: 'login',
        ip: clientIP,
        remaining: rateLimitResult.remaining,
        resetTime: rateLimitResult.resetTime,
      },
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      {
        error: 'Too many login attempts. Please try again later.',
        code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
        retryAfter: rateLimitResult.resetTime,
      },
      { status: 429 }
    );
  }

  // Validate request body
  const validation = validateForm(loginSchema, body);
  if (!validation.success) {
    return NextResponse.json(
      {
        error: 'Validation failed',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: validation.errors,
      },
      { status: 400 }
    );
  }

  const { email, password } = validation.data;

  try {
    // Attempt login with Supabase
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      // Track failed login attempt
      await analyticsService.trackEvent({
        event: 'auth_login_failed',
        properties: {
          email,
          error: error.message,
          ip: clientIP,
        },
        timestamp: new Date().toISOString(),
      });

      return NextResponse.json(
        {
          error: error.message,
          code: ERROR_CODES.AUTH_INVALID_CREDENTIALS,
        },
        { status: 401 }
      );
    }

    if (!data.user) {
      return NextResponse.json(
        {
          error: 'Login failed',
          code: ERROR_CODES.AUTH_FAILED,
        },
        { status: 401 }
      );
    }

    // Get or create user profile
    let userProfile;
    try {
      userProfile = await dbService.getUserProfile(data.user.id);
    } catch (profileError) {
      // Create profile if it doesn't exist
      userProfile = await dbService.createUserProfile({
        id: data.user.id,
        email: data.user.email!,
        subscription_tier: 'free',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    }

    // Update last login
    await dbService.updateUserProfile(data.user.id, {
      last_login: new Date().toISOString(),
    });

    // Track successful login
    await analyticsService.trackEvent({
      event: 'auth_login_success',
      userId: data.user.id,
      properties: {
        email,
        subscription_tier: userProfile.subscription_tier,
        ip: clientIP,
      },
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      success: true,
      user: userProfile,
      session: data.session,
    });

  } catch (error) {
    console.error('Login error:', error);
    
    await analyticsService.trackError({
      error: getErrorMessage(error),
      context: 'auth_login',
      userId: email,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      {
        error: 'Login failed',
        code: ERROR_CODES.AUTH_FAILED,
      },
      { status: 500 }
    );
  }
}

async function handleRegister(body: any, clientIP: string) {
  // Rate limiting for registration attempts
  const rateLimitResult = await rateLimitService.checkRateLimit(
    clientIP,
    'auth_register',
    'free'
  );

  if (!rateLimitResult.allowed) {
    return NextResponse.json(
      {
        error: 'Too many registration attempts. Please try again later.',
        code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
        retryAfter: rateLimitResult.resetTime,
      },
      { status: 429 }
    );
  }

  // Validate request body
  const validation = validateForm(registerSchema, body);
  if (!validation.success) {
    return NextResponse.json(
      {
        error: 'Validation failed',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: validation.errors,
      },
      { status: 400 }
    );
  }

  const { email, password, fullName } = validation.data;

  try {
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      return NextResponse.json(
        {
          error: 'User already exists',
          code: ERROR_CODES.AUTH_USER_EXISTS,
        },
        { status: 409 }
      );
    }

    // Register with Supabase
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    });

    if (error) {
      await analyticsService.trackEvent({
        event: 'auth_register_failed',
        properties: {
          email,
          error: error.message,
          ip: clientIP,
        },
        timestamp: new Date().toISOString(),
      });

      return NextResponse.json(
        {
          error: error.message,
          code: ERROR_CODES.AUTH_FAILED,
        },
        { status: 400 }
      );
    }

    if (!data.user) {
      return NextResponse.json(
        {
          error: 'Registration failed',
          code: ERROR_CODES.AUTH_FAILED,
        },
        { status: 400 }
      );
    }

    // Create user profile
    const userProfile = await dbService.createUserProfile({
      id: data.user.id,
      email: data.user.email!,
      full_name: fullName,
      subscription_tier: 'free',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    // Track successful registration
    await analyticsService.trackEvent({
      event: 'auth_register_success',
      userId: data.user.id,
      properties: {
        email,
        full_name: fullName,
        ip: clientIP,
      },
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      success: true,
      message: 'Registration successful. Please check your email to verify your account.',
      user: userProfile,
      needsVerification: !data.session,
    });

  } catch (error) {
    console.error('Registration error:', error);
    
    await analyticsService.trackError({
      error: getErrorMessage(error),
      context: 'auth_register',
      userId: email,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      {
        error: 'Registration failed',
        code: ERROR_CODES.AUTH_FAILED,
      },
      { status: 500 }
    );
  }
}

async function handleForgotPassword(body: any, clientIP: string) {
  // Rate limiting for password reset attempts
  const rateLimitResult = await rateLimitService.checkRateLimit(
    clientIP,
    'auth_forgot_password',
    'free'
  );

  if (!rateLimitResult.allowed) {
    return NextResponse.json(
      {
        error: 'Too many password reset attempts. Please try again later.',
        code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
        retryAfter: rateLimitResult.resetTime,
      },
      { status: 429 }
    );
  }

  // Validate request body
  const validation = validateForm(forgotPasswordSchema, body);
  if (!validation.success) {
    return NextResponse.json(
      {
        error: 'Validation failed',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: validation.errors,
      },
      { status: 400 }
    );
  }

  const { email } = validation.data;

  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password`,
    });

    if (error) {
      await analyticsService.trackEvent({
        event: 'auth_forgot_password_failed',
        properties: {
          email,
          error: error.message,
          ip: clientIP,
        },
        timestamp: new Date().toISOString(),
      });

      return NextResponse.json(
        {
          error: error.message,
          code: ERROR_CODES.AUTH_FAILED,
        },
        { status: 400 }
      );
    }

    // Track password reset request
    await analyticsService.trackEvent({
      event: 'auth_forgot_password_success',
      properties: {
        email,
        ip: clientIP,
      },
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      success: true,
      message: 'Password reset email sent. Please check your inbox.',
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    
    await analyticsService.trackError({
      error: getErrorMessage(error),
      context: 'auth_forgot_password',
      userId: email,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      {
        error: 'Failed to send password reset email',
        code: ERROR_CODES.AUTH_FAILED,
      },
      { status: 500 }
    );
  }
}

async function handleLogout() {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      return NextResponse.json(
        {
          error: error.message,
          code: ERROR_CODES.AUTH_FAILED,
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Logged out successfully',
    });

  } catch (error) {
    console.error('Logout error:', error);
    
    return NextResponse.json(
      {
        error: 'Logout failed',
        code: ERROR_CODES.AUTH_FAILED,
      },
      { status: 500 }
    );
  }
}
