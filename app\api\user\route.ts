import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { supabase } from '@/lib/supabase';
import { DatabaseService } from '@/lib/supabase';
import { RateLimitService } from '@/lib/rateLimit';
import { AnalyticsService } from '@/lib/analytics';
import { validateForm } from '@/utils/validation';
import { getErrorMessage } from '@/utils/helpers';
import { ERROR_CODES, HTTP_STATUS } from '@/utils/constants';

const dbService = new DatabaseService();
const rateLimitService = new RateLimitService();
const analyticsService = new AnalyticsService();

// Validation schemas
const updateProfileSchema = z.object({
  full_name: z.string().min(1).max(100).optional(),
  avatar_url: z.string().url().optional().or(z.literal('')),
  website: z.string().url().optional().or(z.literal('')),
  bio: z.string().max(500).optional(),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'system']).optional(),
    defaultEnhancementLevel: z.enum(['basic', 'advanced', 'expert']).optional(),
    emailNotifications: z.boolean().optional(),
    analyticsOptIn: z.boolean().optional(),
  }).optional(),
});

const updateSubscriptionSchema = z.object({
  subscription_tier: z.enum(['free', 'pro', 'business']),
});

// GET /api/user - Get current user profile and usage data
export async function GET(request: NextRequest) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_REQUIRED,
          message: 'Authentication required',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_INVALID,
          message: 'Invalid authentication',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Rate limiting
    const rateLimitResult = await rateLimitService.checkRateLimit(
      clientIP,
      'user_get',
      'free'
    );

    if (!rateLimitResult.allowed) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
          message: 'Rate limit exceeded',
          retryAfter: rateLimitResult.resetTime,
        },
      }, { status: HTTP_STATUS.TOO_MANY_REQUESTS });
    }

    // Get user data with profile
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles (*)
      `)
      .eq('id', user.id)
      .single();

    if (userError) {
      console.error('User fetch error:', userError);
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.DATABASE_ERROR,
          message: 'Failed to fetch user data',
        },
      }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
    }

    // Get usage statistics
    const { data: usageStats, error: usageError } = await supabase
      .from('prompt_enhancements')
      .select('id, created_at')
      .eq('user_id', user.id)
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

    if (usageError) {
      console.error('Usage stats error:', usageError);
    }

    // Get template count
    const { count: templateCount } = await supabase
      .from('prompt_templates')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    // Calculate usage metrics
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    const monthlyUsage = usageStats?.filter(stat => {
      const statDate = new Date(stat.created_at);
      return statDate.getMonth() === currentMonth && statDate.getFullYear() === currentYear;
    }).length || 0;

    const weeklyUsage = usageStats?.filter(stat => {
      const statDate = new Date(stat.created_at);
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      return statDate >= weekAgo;
    }).length || 0;

    const dailyUsage = usageStats?.filter(stat => {
      const statDate = new Date(stat.created_at);
      const today = new Date();
      return statDate.toDateString() === today.toDateString();
    }).length || 0;

    // Track analytics
    await analyticsService.trackEvent('user_profile_viewed', {
      userId: user.id,
      subscriptionTier: userData.subscription_tier,
    });

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: userData.id,
          email: userData.email,
          subscription_tier: userData.subscription_tier,
          monthly_usage: userData.monthly_usage,
          last_reset_date: userData.last_reset_date,
          created_at: userData.created_at,
          profile: userData.user_profiles?.[0] || null,
        },
        usage: {
          monthly: monthlyUsage,
          weekly: weeklyUsage,
          daily: dailyUsage,
          templates: templateCount || 0,
        },
        limits: {
          monthly_enhancements: userData.subscription_tier === 'free' ? 50 : 
                               userData.subscription_tier === 'pro' ? 500 : 5000,
          templates: userData.subscription_tier === 'free' ? 5 : 
                    userData.subscription_tier === 'pro' ? 50 : 500,
          api_access: userData.subscription_tier !== 'free',
        },
      },
    });

  } catch (error) {
    console.error('User GET error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
    }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
  }
}

// PUT /api/user - Update user profile
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validation = updateProfileSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.VALIDATION_ERROR,
          message: 'Invalid profile data',
          details: validation.error.errors,
        },
      }, { status: HTTP_STATUS.BAD_REQUEST });
    }

    // Get user from session
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_REQUIRED,
          message: 'Authentication required',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_INVALID,
          message: 'Invalid authentication',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Rate limiting
    const rateLimitResult = await rateLimitService.checkRateLimit(
      clientIP,
      'user_update',
      'free'
    );

    if (!rateLimitResult.allowed) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
          message: 'Rate limit exceeded',
          retryAfter: rateLimitResult.resetTime,
        },
      }, { status: HTTP_STATUS.TOO_MANY_REQUESTS });
    }

    const profileData = validation.data;

    // Update or create user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .upsert({
        user_id: user.id,
        ...profileData,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (profileError) {
      console.error('Profile update error:', profileError);
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.DATABASE_ERROR,
          message: 'Failed to update profile',
        },
      }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
    }

    // Track analytics
    await analyticsService.trackEvent('user_profile_updated', {
      userId: user.id,
      updatedFields: Object.keys(profileData),
    });

    return NextResponse.json({
      success: true,
      data: { profile },
    });

  } catch (error) {
    console.error('User PUT error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
    }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
  }
}

// DELETE /api/user - Delete user account
export async function DELETE(request: NextRequest) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_REQUIRED,
          message: 'Authentication required',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_INVALID,
          message: 'Invalid authentication',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    // Delete user data (cascading deletes will handle related records)
    const { error: deleteError } = await supabase.auth.admin.deleteUser(user.id);

    if (deleteError) {
      console.error('User deletion error:', deleteError);
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.DATABASE_ERROR,
          message: 'Failed to delete account',
        },
      }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
    }

    // Track analytics
    await analyticsService.trackEvent('user_account_deleted', {
      userId: user.id,
    });

    return NextResponse.json({
      success: true,
      message: 'Account deleted successfully',
    });

  } catch (error) {
    console.error('User DELETE error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
    }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
  }
}
