import { z } from 'zod';

// Types for prompt enhancement
export interface EnhancementRule {
  id: string;
  name: string;
  pattern: RegExp;
  enhancement: (match: string, context?: PromptContext) => string;
  priority: number;
  category: EnhancementCategory;
  enabled: boolean;
}

export interface PromptContext {
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  domain: string;
  previousPrompts: string[];
  targetAI: 'openai' | 'anthropic' | 'google' | 'huggingface' | 'generic';
}

export type EnhancementCategory = 
  | 'clarity' 
  | 'specificity' 
  | 'structure' 
  | 'context' 
  | 'examples' 
  | 'constraints';

export interface EnhancementResult {
  originalPrompt: string;
  enhancedPrompt: string;
  appliedRules: string[];
  qualityScore: number;
  suggestions: string[];
  estimatedTokens: number;
}

// Validation schema
export const PromptSchema = z.object({
  text: z.string().min(1, 'Prompt cannot be empty').max(10000, 'Prompt too long'),
  context: z.object({
    userLevel: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
    domain: z.string().optional(),
    targetAI: z.enum(['openai', 'anthropic', 'google', 'huggingface', 'generic']).optional(),
  }).optional(),
});

// Enhancement rules database
export const enhancementRules: EnhancementRule[] = [
  {
    id: 'clarity-vague-request',
    name: 'Clarify Vague Requests',
    pattern: /^(write|create|make|do|help)\s+(.{1,30})$/i,
    enhancement: (match, context) => {
      const action = match.split(' ')[0].toLowerCase();
      const subject = match.substring(action.length + 1);
      return `Please ${action} a comprehensive ${subject} that includes:
1. Clear structure and organization
2. Detailed explanations with examples
3. Practical applications or use cases
4. Step-by-step guidance where applicable
5. Any important considerations or limitations

Make the content accessible for ${context?.userLevel || 'general'} audience and ensure it's actionable.`;
    },
    priority: 1,
    category: 'clarity',
    enabled: true,
  },
  {
    id: 'specificity-explain-request',
    name: 'Enhance Explanation Requests',
    pattern: /^(explain|tell me about|what is|describe)\s+(.+)$/i,
    enhancement: (match, context) => {
      const topic = match.replace(/^(explain|tell me about|what is|describe)\s+/i, '');
      return `Provide a comprehensive explanation of ${topic} that covers:

**Definition & Core Concepts:**
- Clear definition and key terminology
- Fundamental principles or mechanisms

**Practical Context:**
- Real-world examples and applications
- Common use cases and scenarios

**Implementation Details:**
- Step-by-step processes (if applicable)
- Best practices and recommendations

**Additional Insights:**
- Common misconceptions or pitfalls
- Related concepts or alternatives
- Future trends or developments (if relevant)

Structure your response with clear headings and make it suitable for ${context?.userLevel || 'intermediate'} level understanding.`;
    },
    priority: 1,
    category: 'specificity',
    enabled: true,
  },
  {
    id: 'structure-list-request',
    name: 'Structure List Requests',
    pattern: /^(list|give me|show me)\s+(.+)$/i,
    enhancement: (match, context) => {
      const request = match.replace(/^(list|give me|show me)\s+/i, '');
      return `Create a comprehensive list of ${request} with the following structure:

For each item, provide:
- **Name/Title**: Clear identification
- **Description**: Brief but informative explanation
- **Key Features**: Main characteristics or benefits
- **Use Case**: When/why to use it
- **Example**: Practical illustration (if applicable)

Please organize the list logically (by importance, category, or chronology) and include 8-12 high-quality items. Add a brief introduction explaining the context and a conclusion summarizing key takeaways.`;
    },
    priority: 2,
    category: 'structure',
    enabled: true,
  },
  {
    id: 'context-how-to-request',
    name: 'Enhance How-To Requests',
    pattern: /^(how to|how do i|how can i)\s+(.+)$/i,
    enhancement: (match, context) => {
      const task = match.replace(/^(how to|how do i|how can i)\s+/i, '');
      return `Provide a detailed guide on how to ${task}:

**Prerequisites:**
- Required knowledge, skills, or tools
- Preparation steps

**Step-by-Step Instructions:**
1. [Provide numbered steps with clear actions]
2. [Include specific details and parameters]
3. [Add troubleshooting tips for each step]

**Best Practices:**
- Professional tips and recommendations
- Common mistakes to avoid
- Optimization strategies

**Examples:**
- Practical scenarios or case studies
- Before/after comparisons (if applicable)

**Additional Resources:**
- Related tools or techniques
- Further learning materials

Tailor the complexity to ${context?.userLevel || 'intermediate'} level and include warnings for any potential risks or challenges.`;
    },
    priority: 1,
    category: 'structure',
    enabled: true,
  },
  {
    id: 'examples-comparison-request',
    name: 'Enhance Comparison Requests',
    pattern: /^(compare|difference between|vs|versus)\s+(.+)$/i,
    enhancement: (match, context) => {
      const subjects = match.replace(/^(compare|difference between|vs|versus)\s+/i, '');
      return `Provide a comprehensive comparison of ${subjects}:

**Overview:**
- Brief introduction to each option
- Context for the comparison

**Detailed Comparison:**
| Aspect | Option A | Option B |
|--------|----------|----------|
| [Key criteria] | [Details] | [Details] |

**Pros and Cons:**
**Option A:**
- ✅ Advantages
- ❌ Disadvantages

**Option B:**
- ✅ Advantages  
- ❌ Disadvantages

**Use Cases:**
- When to choose Option A
- When to choose Option B
- Scenarios where neither is ideal

**Recommendation:**
- Summary of key differences
- Decision framework
- Final recommendation based on common use cases

Include specific examples and quantitative data where possible.`;
    },
    priority: 2,
    category: 'examples',
    enabled: true,
  },
  {
    id: 'constraints-short-prompt',
    name: 'Expand Short Prompts',
    pattern: /^.{1,20}$/,
    enhancement: (match, context) => {
      return `Regarding "${match}", please provide a comprehensive response that includes:

**Core Information:**
- Detailed explanation of the topic
- Key concepts and terminology
- Current relevance and importance

**Practical Insights:**
- Real-world applications
- Common scenarios or examples
- Best practices or recommendations

**Context and Background:**
- Historical perspective (if relevant)
- Related concepts or alternatives
- Current trends or developments

**Actionable Guidance:**
- Step-by-step processes (if applicable)
- Tips for implementation
- Common pitfalls to avoid

Please structure your response clearly with headings and provide specific, actionable information suitable for ${context?.userLevel || 'general'} audience.`;
    },
    priority: 3,
    category: 'constraints',
    enabled: true,
  },
];

// Quality scoring algorithm
export function calculateQualityScore(
  originalPrompt: string,
  enhancedPrompt: string,
  appliedRules: string[]
): number {
  let score = 50; // Base score

  // Length and detail bonus
  const lengthRatio = enhancedPrompt.length / Math.max(originalPrompt.length, 1);
  score += Math.min(lengthRatio * 10, 30);

  // Structure bonus
  if (enhancedPrompt.includes('**') || enhancedPrompt.includes('##')) score += 10;
  if (enhancedPrompt.includes('1.') || enhancedPrompt.includes('-')) score += 5;

  // Specificity bonus
  const specificWords = ['specific', 'detailed', 'comprehensive', 'step-by-step', 'example'];
  const specificCount = specificWords.filter(word => 
    enhancedPrompt.toLowerCase().includes(word)
  ).length;
  score += specificCount * 3;

  // Applied rules bonus
  score += appliedRules.length * 5;

  return Math.min(Math.max(score, 0), 100);
}

// Token estimation
export function estimateTokens(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4);
}

// Main enhancement function
export function enhancePrompt(
  originalPrompt: string,
  context?: Partial<PromptContext>
): EnhancementResult {
  // Validate input
  const validation = PromptSchema.safeParse({ 
    text: originalPrompt, 
    context 
  });
  
  if (!validation.success) {
    throw new Error(`Invalid prompt: ${validation.error.errors[0]?.message}`);
  }

  const fullContext: PromptContext = {
    userLevel: 'intermediate',
    domain: 'general',
    previousPrompts: [],
    targetAI: 'generic',
    ...context,
  };

  // Find applicable rules
  const applicableRules = enhancementRules
    .filter(rule => rule.enabled && rule.pattern.test(originalPrompt))
    .sort((a, b) => a.priority - b.priority);

  let enhancedPrompt = originalPrompt;
  const appliedRules: string[] = [];
  const suggestions: string[] = [];

  if (applicableRules.length > 0) {
    // Apply the highest priority rule
    const rule = applicableRules[0];
    enhancedPrompt = rule.enhancement(originalPrompt, fullContext);
    appliedRules.push(rule.id);

    // Add suggestions from other applicable rules
    applicableRules.slice(1, 3).forEach(rule => {
      suggestions.push(`Consider ${rule.name.toLowerCase()}`);
    });
  } else {
    // Fallback enhancement for unmatched prompts
    enhancedPrompt = `${originalPrompt}

Please provide a detailed, well-structured response that includes:
- Clear explanations with specific examples
- Practical applications or use cases
- Step-by-step guidance where applicable
- Important considerations or limitations

Structure your answer with clear headings and make it comprehensive yet accessible.`;
    appliedRules.push('fallback-enhancement');
  }

  // Calculate quality score
  const qualityScore = calculateQualityScore(originalPrompt, enhancedPrompt, appliedRules);

  // Estimate tokens
  const estimatedTokens = estimateTokens(enhancedPrompt);

  return {
    originalPrompt,
    enhancedPrompt,
    appliedRules,
    qualityScore,
    suggestions,
    estimatedTokens,
  };
}
