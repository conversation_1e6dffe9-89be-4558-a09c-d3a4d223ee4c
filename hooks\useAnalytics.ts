'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './useAuth';
import { useRateLimit } from './useRateLimit';

interface AnalyticsEvent {
  event: string;
  properties?: Record<string, any>;
  timestamp?: number;
}

interface AnalyticsData {
  totalEnhancements: number;
  totalTemplates: number;
  totalGenerations: number;
  monthlyUsage: number;
  weeklyUsage: number;
  dailyUsage: number;
  popularCategories: Array<{ category: string; count: number }>;
  recentActivity: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: string;
  }>;
}

interface UseAnalyticsOptions {
  enableAutoTracking?: boolean;
  batchSize?: number;
  flushInterval?: number;
}

interface UseAnalyticsReturn {
  // Tracking functions
  trackEvent: (event: string, properties?: Record<string, any>) => Promise<void>;
  trackPageView: (page: string, properties?: Record<string, any>) => Promise<void>;
  trackError: (error: Error, context?: string) => Promise<void>;
  trackTiming: (name: string, duration: number, properties?: Record<string, any>) => Promise<void>;
  
  // Analytics data
  data: AnalyticsData | null;
  isLoading: boolean;
  error: string | null;
  
  // Control functions
  fetchAnalytics: () => Promise<void>;
  clearLocalEvents: () => void;
  flush: () => Promise<void>;
  
  // State
  pendingEvents: number;
  isOnline: boolean;
}

// Local storage keys
const EVENTS_STORAGE_KEY = 'ai-prompt-enhancer-analytics-events';
const PREFERENCES_STORAGE_KEY = 'ai-prompt-enhancer-analytics-preferences';

export function useAnalytics({
  enableAutoTracking = true,
  batchSize = 10,
  flushInterval = 30000, // 30 seconds
}: UseAnalyticsOptions = {}): UseAnalyticsReturn {
  const { user } = useAuth();
  const { checkRateLimit } = useRateLimit({ action: 'analytics_track' });
  
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pendingEvents, setPendingEvents] = useState(0);
  const [isOnline, setIsOnline] = useState(typeof navigator !== 'undefined' ? navigator.onLine : true);
  
  const eventQueueRef = useRef<AnalyticsEvent[]>([]);
  const flushTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Load pending events from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(EVENTS_STORAGE_KEY);
      if (stored) {
        const events = JSON.parse(stored);
        eventQueueRef.current = events;
        setPendingEvents(events.length);
      }
    } catch (error) {
      console.error('Failed to load pending analytics events:', error);
    }
  }, []);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Save events to localStorage
  const saveEventsToStorage = useCallback(() => {
    try {
      localStorage.setItem(EVENTS_STORAGE_KEY, JSON.stringify(eventQueueRef.current));
    } catch (error) {
      console.error('Failed to save analytics events:', error);
    }
  }, []);

  // Add event to queue
  const queueEvent = useCallback((event: AnalyticsEvent) => {
    eventQueueRef.current.push({
      ...event,
      timestamp: event.timestamp || Date.now(),
    });
    setPendingEvents(eventQueueRef.current.length);
    saveEventsToStorage();

    // Auto-flush if batch size reached
    if (eventQueueRef.current.length >= batchSize) {
      flush();
    }
  }, [batchSize, saveEventsToStorage]);

  // Flush events to server
  const flush = useCallback(async () => {
    if (eventQueueRef.current.length === 0 || !isOnline) {
      return;
    }

    // Check rate limit
    const allowed = await checkRateLimit();
    if (!allowed) {
      console.warn('Analytics rate limit exceeded, events will be sent later');
      return;
    }

    const eventsToSend = [...eventQueueRef.current];
    
    try {
      const response = await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(user && { 'Authorization': `Bearer ${user.access_token}` }),
        },
        body: JSON.stringify({
          events: eventsToSend,
          userId: user?.id,
        }),
      });

      if (response.ok) {
        // Clear sent events
        eventQueueRef.current = [];
        setPendingEvents(0);
        localStorage.removeItem(EVENTS_STORAGE_KEY);
      } else {
        console.error('Failed to send analytics events:', response.statusText);
      }
    } catch (error) {
      console.error('Analytics flush error:', error);
    }
  }, [isOnline, checkRateLimit, user]);

  // Set up auto-flush interval
  useEffect(() => {
    if (flushTimeoutRef.current) {
      clearInterval(flushTimeoutRef.current);
    }

    flushTimeoutRef.current = setInterval(() => {
      if (eventQueueRef.current.length > 0) {
        flush();
      }
    }, flushInterval);

    return () => {
      if (flushTimeoutRef.current) {
        clearInterval(flushTimeoutRef.current);
      }
    };
  }, [flush, flushInterval]);

  // Track event
  const trackEvent = useCallback(async (event: string, properties?: Record<string, any>) => {
    // Check user preferences
    const preferences = localStorage.getItem(PREFERENCES_STORAGE_KEY);
    if (preferences) {
      const parsed = JSON.parse(preferences);
      if (!parsed.analyticsOptIn) {
        return; // User opted out
      }
    }

    queueEvent({
      event,
      properties: {
        ...properties,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        sessionId: sessionStorage.getItem('session-id') || 'anonymous',
        userId: user?.id,
      },
    });
  }, [queueEvent, user]);

  // Track page view
  const trackPageView = useCallback(async (page: string, properties?: Record<string, any>) => {
    await trackEvent('page_view', {
      page,
      referrer: document.referrer,
      ...properties,
    });
  }, [trackEvent]);

  // Track error
  const trackError = useCallback(async (error: Error, context?: string) => {
    await trackEvent('error', {
      errorMessage: error.message,
      errorStack: error.stack,
      context,
      url: window.location.href,
    });
  }, [trackEvent]);

  // Track timing
  const trackTiming = useCallback(async (name: string, duration: number, properties?: Record<string, any>) => {
    await trackEvent('timing', {
      name,
      duration,
      ...properties,
    });
  }, [trackEvent]);

  // Fetch analytics data
  const fetchAnalytics = useCallback(async () => {
    if (!user) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/analytics', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.error?.message || 'Failed to fetch analytics');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Analytics fetch failed';
      setError(errorMessage);
      console.error('Analytics fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Clear local events
  const clearLocalEvents = useCallback(() => {
    eventQueueRef.current = [];
    setPendingEvents(0);
    localStorage.removeItem(EVENTS_STORAGE_KEY);
  }, []);

  // Auto-track page views
  useEffect(() => {
    if (enableAutoTracking && typeof window !== 'undefined') {
      trackPageView(window.location.pathname);
    }
  }, [enableAutoTracking, trackPageView]);

  // Auto-fetch analytics data when user changes
  useEffect(() => {
    if (user) {
      fetchAnalytics();
    } else {
      setData(null);
    }
  }, [user, fetchAnalytics]);

  // Flush events when component unmounts or page unloads
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (eventQueueRef.current.length > 0) {
        // Use sendBeacon for reliable delivery on page unload
        if (navigator.sendBeacon) {
          const payload = JSON.stringify({
            events: eventQueueRef.current,
            userId: user?.id,
          });
          navigator.sendBeacon('/api/analytics', payload);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      handleBeforeUnload(); // Flush on component unmount
    };
  }, [user]);

  return {
    // Tracking functions
    trackEvent,
    trackPageView,
    trackError,
    trackTiming,
    
    // Analytics data
    data,
    isLoading,
    error,
    
    // Control functions
    fetchAnalytics,
    clearLocalEvents,
    flush,
    
    // State
    pendingEvents,
    isOnline,
  };
}
