import { NextRequest, NextResponse } from 'next/server';

// GET /api/robots - Generate robots.txt
export async function GET(request: NextRequest) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-prompt-enhancer.vercel.app';
    
    const robotsTxt = `# Robots.txt for AI Prompt Enhancer
# Generated automatically

User-agent: *
Allow: /

# Allow all search engines to crawl public pages
Allow: /
Allow: /login
Allow: /register
Allow: /templates/*

# Disallow private/sensitive areas
Disallow: /dashboard
Disallow: /api/
Disallow: /admin
Disallow: /_next/
Disallow: /private/

# Disallow authentication pages for bots
Disallow: /auth/
Disallow: /logout

# Allow specific bots full access
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

# Crawl delay for general bots
Crawl-delay: 1

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Additional sitemaps if needed
# Sitemap: ${baseUrl}/sitemap-templates.xml
# Sitemap: ${baseUrl}/sitemap-categories.xml
`;

    return new NextResponse(robotsTxt, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache for 24 hours
      },
    });

  } catch (error) {
    console.error('Robots.txt generation error:', error);
    
    // Return a basic robots.txt on error
    const basicRobots = `User-agent: *
Allow: /
Disallow: /api/
Disallow: /dashboard
Disallow: /_next/

Sitemap: ${process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-prompt-enhancer.vercel.app'}/sitemap.xml
`;

    return new NextResponse(basicRobots, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400',
      },
    });
  }
}
