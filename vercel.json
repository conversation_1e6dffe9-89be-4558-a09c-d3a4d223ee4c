{"version": 2, "name": "ai-prompt-enhancer", "alias": ["ai-prompt-enhancer.vercel.app"], "regions": ["iad1", "sfo1", "lhr1"], "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "env": {"NODE_ENV": "production", "NEXT_PUBLIC_SITE_URL": "https://ai-prompt-enhancer.vercel.app"}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}, "app/api/enhance/route.ts": {"maxDuration": 60}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://ai-prompt-enhancer.vercel.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}, {"key": "Access-Control-Max-Age", "value": "86400"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/docs", "destination": "/api-docs", "permanent": true}, {"source": "/github", "destination": "https://github.com/ai-prompt-enhancer/ai-prompt-enhancer", "permanent": false}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "crons": [{"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}, {"path": "/api/cron/analytics", "schedule": "0 1 * * *"}]}