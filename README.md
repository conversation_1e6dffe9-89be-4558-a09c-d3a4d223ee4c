# 🚀 AI Prompt Enhancer

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/HectorTa1989/ai-prompt-enhancer)
[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/HectorTa1989/ai-prompt-enhancer)

> Transform your AI prompts into powerful, detailed instructions that generate better responses from ChatGPT, Claude, Gemini, and other AI models.

## 🌟 Features

- **Smart Prompt Enhancement**: Advanced algorithms analyze and improve your prompts
- **Multi-AI Provider Support**: Works with OpenAI, Anthropic, Google, and Hugging Face
- **Rate Limiting & Usage Tracking**: Built-in quota management
- **SEO/GEO/AEO Optimized**: Global reach with search engine optimization
- **Serverless Architecture**: Deploy on Vercel, Netlify, AWS, or GCP
- **Real-time Analytics**: Track enhancement quality and usage patterns

## 🏆 Domain Name Suggestions

### Available Premium Domains (Recommended)
- `promptboost.ai` - Short, brandable, AI-focused
- `enhanceprompt.com` - Clear value proposition
- `promptgenius.io` - Memorable and viral
- `aiprompthub.com` - Community-focused
- `smartprompts.app` - Modern and clean

### Budget-Friendly Alternatives
- `promptenhancer.dev` - Developer-focused
- `betterprompter.com` - Clear benefit
- `promptoptimizer.net` - Technical appeal
- `aiprompttool.org` - Utility-focused
- `promptcraft.co` - Creative and short

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Next.js 14 App] --> B[React Components]
        B --> C[Tailwind CSS]
        A --> D[TypeScript]
    end
    
    subgraph "API Layer"
        E[Serverless Functions] --> F[Rate Limiting]
        E --> G[Authentication]
        E --> H[Prompt Enhancement Engine]
    end
    
    subgraph "Enhancement Engine"
        H --> I[Pattern Matching]
        H --> J[Context Analysis]
        H --> K[Template System]
        H --> L[Quality Scoring]
    end
    
    subgraph "Database Layer"
        M[(Supabase PostgreSQL)] --> N[Users Table]
        M --> O[Prompt Enhancements]
        M --> P[Templates]
        M --> Q[Usage Analytics]
    end
    
    subgraph "External APIs"
        R[OpenAI API]
        S[Anthropic Claude]
        T[Google Gemini]
        U[Hugging Face]
    end
    
    A --> E
    E --> M
    E --> R
    E --> S
    E --> T
    E --> U
```

## 🔄 Workflow

```mermaid
flowchart TD
    A[User Input] --> B{User Authenticated?}
    B -->|No| C[Redirect to Login]
    B -->|Yes| D[Check Rate Limits]
    
    D --> E{Within Limits?}
    E -->|No| F[Return Rate Limit Error]
    E -->|Yes| G[Analyze Original Prompt]
    
    G --> H[Apply Enhancement Rules]
    H --> I[Pattern Matching]
    I --> J[Context Enhancement]
    J --> K[Template Application]
    K --> L[Quality Scoring]
    
    L --> M[Generate Enhanced Prompt]
    M --> N[Store in Database]
    N --> O[Update Usage Counter]
    
    O --> P{Send to AI Provider?}
    P -->|No| Q[Return Enhanced Prompt]
    P -->|Yes| R[Select AI Provider]
    
    R --> S[Format Request]
    S --> T[Send to AI API]
    T --> U[Parse Response]
    U --> V[Store Response]
    V --> W[Return Results]
    
    Q --> X[Display to User]
    W --> X
```

## 📁 Project Structure

```
ai-prompt-enhancer/
├── 📁 app/                          # Next.js 14 App Router
│   ├── 📁 (auth)/
│   │   ├── login/page.tsx
│   │   └── register/page.tsx
│   ├── 📁 dashboard/
│   │   ├── page.tsx
│   │   └── analytics/page.tsx
│   ├── 📁 api/
│   │   ├── enhance/route.ts
│   │   ├── auth/route.ts
│   │   └── analytics/route.ts
│   ├── globals.css
│   ├── layout.tsx
│   ├── page.tsx
│   └── sitemap.ts
├── 📁 components/
│   ├── ui/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   └── Modal.tsx
│   ├── PromptEnhancer.tsx
│   ├── Header.tsx
│   ├── Footer.tsx
│   └── SEOHead.tsx
├── 📁 lib/
│   ├── promptEnhancer.ts
│   ├── aiProviders.ts
│   ├── rateLimit.ts
│   ├── supabase.ts
│   ├── analytics.ts
│   └── seo.ts
├── 📁 hooks/
│   ├── useAuth.ts
│   ├── useRateLimit.ts
│   └── useAnalytics.ts
├── 📁 types/
│   ├── index.ts
│   └── database.ts
├── 📁 utils/
│   ├── constants.ts
│   ├── helpers.ts
│   └── validation.ts
├── 📁 public/
│   ├── favicon.ico
│   ├── robots.txt
│   └── sitemap.xml
├── 📁 docs/
│   ├── API.md
│   └── DEPLOYMENT.md
├── package.json
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── .env.example
├── vercel.json
├── netlify.toml
└── README.md
```

## 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/HectorTa1989/ai-prompt-enhancer.git
   cd ai-prompt-enhancer
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your API keys
   ```

4. **Run development server**
   ```bash
   npm run dev
   ```

5. **Open [http://localhost:3000](http://localhost:3000)**

## 🔧 Environment Variables

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# AI Providers (Optional - for direct API calls)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_AI_API_KEY=your_google_ai_key

# Rate Limiting
UPSTASH_REDIS_REST_URL=your_redis_url
UPSTASH_REDIS_REST_TOKEN=your_redis_token

# Analytics
GOOGLE_ANALYTICS_ID=your_ga_id
```

## 🌍 Deployment

### Vercel (Recommended)
```bash
npm run build
vercel --prod
```

### Netlify
```bash
npm run build
netlify deploy --prod --dir=out
```

### AWS Lambda
```bash
npm run build:aws
serverless deploy
```

### Google Cloud Functions
```bash
npm run build:gcp
gcloud functions deploy ai-prompt-enhancer
```

## 📊 Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Serverless Functions, Supabase
- **Database**: PostgreSQL (Supabase)
- **Authentication**: Supabase Auth
- **Rate Limiting**: Upstash Redis
- **Analytics**: Google Analytics, Custom Analytics
- **SEO**: Next.js SEO, Structured Data
- **Deployment**: Vercel, Netlify, AWS, GCP

## 📦 Installation & Setup

### Prerequisites

- Node.js 18+ and npm
- Supabase account
- Upstash Redis account
- AI API keys (OpenAI, Anthropic, etc.)

### Environment Variables

Create a `.env.local` file in the root directory:

```env
# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="AI Prompt Enhancer"

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Redis (Upstash)
UPSTASH_REDIS_REST_URL=your_upstash_redis_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token

# AI API Keys
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key
HUGGINGFACE_API_KEY=your_huggingface_api_key

# Analytics
GOOGLE_ANALYTICS_ID=your_ga_id

# Email (Optional)
RESEND_API_KEY=your_resend_api_key
```

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/HectorTa1989/ai-prompt-enhancer.git
   cd ai-prompt-enhancer
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your actual values
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Project Structure

```
ai-prompt-enhancer/
├── app/                    # Next.js 14 App Router
│   ├── api/               # API routes
│   │   ├── enhance/       # Prompt enhancement endpoint
│   │   ├── auth/          # Authentication endpoints
│   │   └── analytics/     # Analytics endpoints
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Homepage
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   │   ├── Button.tsx    # Button component system
│   │   ├── Input.tsx     # Input component system
│   │   └── Modal.tsx     # Modal component system
│   ├── Header.tsx        # Site header
│   ├── Footer.tsx        # Site footer
│   └── PromptEnhancer.tsx # Main enhancement component
├── lib/                  # Core libraries
│   ├── promptEnhancer.ts # Enhancement engine
│   ├── aiProviders.ts    # AI provider integrations
│   ├── rateLimit.ts      # Rate limiting service
│   ├── supabase.ts       # Database service
│   ├── analytics.ts      # Analytics service
│   └── seo.ts            # SEO utilities
├── hooks/                # Custom React hooks
│   └── useAuth.ts        # Authentication hook
├── types/                # TypeScript type definitions
│   ├── index.ts          # Main types
│   └── database.ts       # Database types
├── utils/                # Utility functions
│   ├── constants.ts      # App constants
│   ├── helpers.ts        # Helper functions
│   └── validation.ts     # Validation schemas
├── vercel.json           # Vercel deployment config
├── netlify.toml          # Netlify deployment config
└── README.md             # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Hector Ta**
- GitHub: [@HectorTa1989](https://github.com/HectorTa1989)
- LinkedIn: [Connect with me](https://linkedin.com/in/hectorta1989)

## ⭐ Show your support

Give a ⭐️ if this project helped you!

---

<p align="center">Made with ❤️ by <a href="https://github.com/HectorTa1989">Hector Ta</a></p>
