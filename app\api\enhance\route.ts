import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

import { enhancePrompt } from '@/lib/promptEnhancer';
import { RateLimitService } from '@/lib/rateLimit';
import { DatabaseService } from '@/lib/supabase';
import { getAnalytics } from '@/lib/analytics';
import { ERROR_CODES, HTTP_STATUS } from '@/utils/constants';
import type { ApiResponse, EnhancementRequest, EnhancementResponse } from '@/types';

// Request validation schema
const enhanceRequestSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required').max(10000, 'Prompt too long'),
  context: z.object({
    userLevel: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
    domain: z.string().optional(),
    targetAI: z.string().optional(),
  }).optional(),
  options: z.object({
    enhancementLevel: z.enum(['basic', 'advanced', 'expert']).optional(),
    includeExamples: z.boolean().optional(),
    maxLength: z.number().min(100).max(50000).optional(),
  }).optional(),
});

// Initialize services
const rateLimitService = new RateLimitService();
const databaseService = new DatabaseService();
const analytics = getAnalytics();

export async function POST(request: NextRequest) {
  const startTime = performance.now();
  
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate request
    const validationResult = enhanceRequestSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: {
          code: ERROR_CODES.VALIDATION_ERROR,
          message: 'Invalid request data',
          details: validationResult.error.errors,
        },
      }, { status: HTTP_STATUS.BAD_REQUEST });
    }

    const { prompt, context, options } = validationResult.data;

    // Get user information from headers or session
    const authHeader = request.headers.get('authorization');
    const userAgent = request.headers.get('user-agent') || '';
    const clientIP = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';

    let userId: string | undefined;
    let userTier = 'free';

    // Extract user ID from auth header if present
    if (authHeader?.startsWith('Bearer ')) {
      try {
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!
        );
        
        const token = authHeader.substring(7);
        const { data: { user }, error } = await supabase.auth.getUser(token);
        
        if (user && !error) {
          userId = user.id;
          
          // Get user subscription tier
          const userRecord = await databaseService.getUser(userId);
          if (userRecord) {
            userTier = userRecord.subscription_tier;
          }
        }
      } catch (error) {
        console.error('Auth error:', error);
        // Continue without authentication for anonymous users
      }
    }

    // Check rate limits
    const rateLimitIdentifier = userId || clientIP;
    const rateLimitResult = await rateLimitService.checkRateLimit(
      rateLimitIdentifier,
      'enhance_prompt',
      userTier
    );

    if (!rateLimitResult.success) {
      // Track rate limit hit
      if (userId) {
        await analytics.trackError(
          new Error('Rate limit exceeded'),
          'prompt_enhancement',
          userId
        );
      }

      return NextResponse.json<ApiResponse>({
        success: false,
        error: {
          code: ERROR_CODES.RATE_LIMIT_ERROR,
          message: 'Rate limit exceeded',
          details: {
            limit: rateLimitResult.limit,
            remaining: rateLimitResult.remaining,
            reset: rateLimitResult.reset,
            retryAfter: rateLimitResult.retryAfter,
          },
        },
        meta: {
          rateLimit: rateLimitResult,
        },
      }, { 
        status: HTTP_STATUS.TOO_MANY_REQUESTS,
        headers: {
          'X-RateLimit-Limit': rateLimitResult.limit.toString(),
          'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
          'X-RateLimit-Reset': rateLimitResult.reset.toISOString(),
          'Retry-After': (rateLimitResult.retryAfter || 60).toString(),
        },
      });
    }

    // Enhance the prompt
    const enhancementResult = enhancePrompt(prompt, {
      userLevel: context?.userLevel || 'intermediate',
      domain: context?.domain,
      targetAI: context?.targetAI || 'general',
      enhancementLevel: options?.enhancementLevel || 'basic',
      includeExamples: options?.includeExamples || false,
      maxLength: options?.maxLength || 5000,
    });

    const processingTime = performance.now() - startTime;

    // Prepare response
    const response: EnhancementResponse = {
      originalPrompt: prompt,
      enhancedPrompt: enhancementResult.enhancedPrompt,
      appliedRules: enhancementResult.appliedRules,
      qualityScore: enhancementResult.qualityScore,
      suggestions: enhancementResult.suggestions,
      estimatedTokens: enhancementResult.estimatedTokens,
      processingTime: Math.round(processingTime),
    };

    // Save enhancement to database if user is authenticated
    if (userId) {
      try {
        await databaseService.savePromptEnhancement({
          user_id: userId,
          original_prompt: prompt,
          enhanced_prompt: enhancementResult.enhancedPrompt,
          applied_rules: enhancementResult.appliedRules,
          estimated_tokens: enhancementResult.estimatedTokens,
        });

        // Track enhancement analytics
        await analytics.trackEnhancement({
          originalPromptLength: prompt.length,
          enhancedPromptLength: enhancementResult.enhancedPrompt.length,
          appliedRules: enhancementResult.appliedRules,
          qualityScore: enhancementResult.qualityScore,
          processingTime: Math.round(processingTime),
          userId,
        });

        // Increment user usage
        await databaseService.incrementUserUsage(userId);
      } catch (dbError) {
        console.error('Database error:', dbError);
        // Don't fail the request if database operations fail
        await analytics.trackError(
          dbError as Error,
          'database_save_enhancement',
          userId
        );
      }
    }

    // Track performance
    analytics.trackPerformance('prompt_enhancement', processingTime, userId);

    return NextResponse.json<ApiResponse<EnhancementResponse>>({
      success: true,
      data: response,
      meta: {
        rateLimit: rateLimitResult,
      },
    }, {
      headers: {
        'X-RateLimit-Limit': rateLimitResult.limit.toString(),
        'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
        'X-RateLimit-Reset': rateLimitResult.reset.toISOString(),
      },
    });

  } catch (error) {
    const processingTime = performance.now() - startTime;
    
    console.error('Enhancement error:', error);

    // Track error
    if (error instanceof Error) {
      analytics.trackError(error, 'prompt_enhancement_api');
    }

    return NextResponse.json<ApiResponse>({
      success: false,
      error: {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
    }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
  }
}

export async function GET(request: NextRequest) {
  // Health check endpoint
  return NextResponse.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
}

export async function OPTIONS(request: NextRequest) {
  // CORS preflight
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
