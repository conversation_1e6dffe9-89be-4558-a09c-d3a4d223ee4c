import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { supabase } from '@/lib/supabase';
import { DatabaseService } from '@/lib/supabase';
import { RateLimitService } from '@/lib/rateLimit';
import { AnalyticsService } from '@/lib/analytics';
import { validateForm } from '@/utils/validation';
import { getErrorMessage } from '@/utils/helpers';
import { ERROR_CODES, HTTP_STATUS } from '@/utils/constants';

const dbService = new DatabaseService();
const rateLimitService = new RateLimitService();
const analyticsService = new AnalyticsService();

// Validation schemas
const createTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(100, 'Name too long'),
  description: z.string().min(1, 'Description is required').max(500, 'Description too long'),
  content: z.string().min(1, 'Template content is required').max(10000, 'Content too long'),
  category: z.string().min(1, 'Category is required').max(50, 'Category too long'),
  tags: z.array(z.string()).max(10, 'Too many tags').optional(),
  isPublic: z.boolean().optional().default(false),
});

const updateTemplateSchema = createTemplateSchema.partial();

const getTemplatesSchema = z.object({
  category: z.string().optional(),
  tags: z.string().optional(), // Comma-separated tags
  search: z.string().optional(),
  page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform(val => val ? Math.min(parseInt(val, 10), 50) : 20),
  sortBy: z.enum(['created_at', 'updated_at', 'name', 'usage_count']).optional().default('created_at'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// GET /api/templates - Get templates with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    // Validate query parameters
    const validation = getTemplatesSchema.safeParse(queryParams);
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.VALIDATION_ERROR,
          message: 'Invalid query parameters',
          details: validation.error.errors,
        },
      }, { status: HTTP_STATUS.BAD_REQUEST });
    }

    const { category, tags, search, page, limit, sortBy, sortOrder } = validation.data;

    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Rate limiting
    const rateLimitResult = await rateLimitService.checkRateLimit(
      clientIP,
      'templates_get',
      'free'
    );

    if (!rateLimitResult.allowed) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
          message: 'Rate limit exceeded',
          retryAfter: rateLimitResult.resetTime,
        },
      }, { status: HTTP_STATUS.TOO_MANY_REQUESTS });
    }

    // Get user from session (optional for public templates)
    const authHeader = request.headers.get('authorization');
    let userId: string | null = null;

    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const { data: { user } } = await supabase.auth.getUser(token);
      userId = user?.id || null;
    }

    // Build query
    let query = supabase
      .from('prompt_templates')
      .select(`
        id,
        name,
        description,
        content,
        category,
        tags,
        is_public,
        usage_count,
        created_at,
        updated_at,
        user_id,
        users!inner(email)
      `);

    // Filter by public templates or user's own templates
    if (userId) {
      query = query.or(`is_public.eq.true,user_id.eq.${userId}`);
    } else {
      query = query.eq('is_public', true);
    }

    // Apply filters
    if (category) {
      query = query.eq('category', category);
    }

    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query = query.overlaps('tags', tagArray);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,content.ilike.%${search}%`);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data: templates, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.DATABASE_ERROR,
          message: 'Failed to fetch templates',
        },
      }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
    }

    // Get total count for pagination
    const { count: totalCount } = await supabase
      .from('prompt_templates')
      .select('*', { count: 'exact', head: true })
      .eq('is_public', true);

    // Track analytics
    await analyticsService.trackEvent('templates_viewed', {
      category,
      tags,
      search,
      page,
      limit,
      resultCount: templates?.length || 0,
      userId,
    });

    return NextResponse.json({
      success: true,
      data: {
        templates: templates || [],
        pagination: {
          page,
          limit,
          total: totalCount || 0,
          totalPages: Math.ceil((totalCount || 0) / limit),
          hasNext: page * limit < (totalCount || 0),
          hasPrev: page > 1,
        },
      },
    });

  } catch (error) {
    console.error('Templates GET error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
    }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
  }
}

// POST /api/templates - Create a new template
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validation = createTemplateSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.VALIDATION_ERROR,
          message: 'Invalid template data',
          details: validation.error.errors,
        },
      }, { status: HTTP_STATUS.BAD_REQUEST });
    }

    // Get user from session
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_REQUIRED,
          message: 'Authentication required',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_INVALID,
          message: 'Invalid authentication',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Rate limiting
    const rateLimitResult = await rateLimitService.checkRateLimit(
      clientIP,
      'templates_create',
      'pro' // Creating templates requires pro tier
    );

    if (!rateLimitResult.allowed) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
          message: 'Rate limit exceeded',
          retryAfter: rateLimitResult.resetTime,
        },
      }, { status: HTTP_STATUS.TOO_MANY_REQUESTS });
    }

    const { name, description, content, category, tags, isPublic } = validation.data;

    // Create template
    const { data: template, error: createError } = await supabase
      .from('prompt_templates')
      .insert({
        name,
        description,
        content,
        category,
        tags: tags || [],
        is_public: isPublic,
        user_id: user.id,
        usage_count: 0,
      })
      .select()
      .single();

    if (createError) {
      console.error('Template creation error:', createError);
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.DATABASE_ERROR,
          message: 'Failed to create template',
        },
      }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
    }

    // Track analytics
    await analyticsService.trackEvent('template_created', {
      templateId: template.id,
      category,
      isPublic,
      userId: user.id,
    });

    return NextResponse.json({
      success: true,
      data: { template },
    }, { status: HTTP_STATUS.CREATED });

  } catch (error) {
    console.error('Templates POST error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
    }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
  }
}
