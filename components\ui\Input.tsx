'use client';

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Eye, EyeOff, AlertCircle, Check, Search, X } from 'lucide-react';

import { cn } from '@/utils/helpers';

const inputVariants = cva(
  'flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: '',
        error: 'border-destructive focus-visible:ring-destructive',
        success: 'border-green-500 focus-visible:ring-green-500',
      },
      size: {
        default: 'h-10',
        sm: 'h-9 px-2 text-xs',
        lg: 'h-11 px-4',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  label?: string;
  error?: string;
  success?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  clearable?: boolean;
  onClear?: () => void;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    variant,
    size,
    type,
    label,
    error,
    success,
    hint,
    leftIcon,
    rightIcon,
    clearable,
    onClear,
    value,
    id,
    ...props
  }, ref) => {
    const inputId = id || React.useId();
    const hasError = !!error;
    const hasSuccess = !!success && !hasError;
    const currentVariant = hasError ? 'error' : hasSuccess ? 'success' : variant;

    const showClearButton = clearable && value && !props.disabled && !props.readOnly;

    return (
      <div className="space-y-2">
        {label && (
          <label
            htmlFor={inputId}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
            {props.required && <span className="text-destructive ml-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}
          
          <input
            type={type}
            className={cn(
              inputVariants({ variant: currentVariant, size }),
              leftIcon && 'pl-10',
              (rightIcon || showClearButton || hasError || hasSuccess) && 'pr-10',
              className
            )}
            ref={ref}
            id={inputId}
            value={value}
            aria-invalid={hasError}
            aria-describedby={
              error ? `${inputId}-error` : 
              success ? `${inputId}-success` : 
              hint ? `${inputId}-hint` : undefined
            }
            {...props}
          />
          
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-1">
            {hasError && (
              <AlertCircle className="h-4 w-4 text-destructive" aria-hidden="true" />
            )}
            {hasSuccess && (
              <Check className="h-4 w-4 text-green-500" aria-hidden="true" />
            )}
            {showClearButton && (
              <button
                type="button"
                onClick={onClear}
                className="text-muted-foreground hover:text-foreground transition-colors"
                aria-label="Clear input"
              >
                <X className="h-4 w-4" />
              </button>
            )}
            {rightIcon && !showClearButton && !hasError && !hasSuccess && rightIcon}
          </div>
        </div>
        
        {error && (
          <p id={`${inputId}-error`} className="text-sm text-destructive" role="alert">
            {error}
          </p>
        )}
        
        {success && !error && (
          <p id={`${inputId}-success`} className="text-sm text-green-600">
            {success}
          </p>
        )}
        
        {hint && !error && !success && (
          <p id={`${inputId}-hint`} className="text-sm text-muted-foreground">
            {hint}
          </p>
        )}
      </div>
    );
  }
);
Input.displayName = 'Input';

// Password Input Component
export interface PasswordInputProps extends Omit<InputProps, 'type' | 'rightIcon'> {
  showStrength?: boolean;
}

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ showStrength = false, value, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);
    
    const getPasswordStrength = (password: string): { score: number; label: string; color: string } => {
      if (!password) return { score: 0, label: '', color: '' };
      
      let score = 0;
      if (password.length >= 8) score++;
      if (/[a-z]/.test(password)) score++;
      if (/[A-Z]/.test(password)) score++;
      if (/[0-9]/.test(password)) score++;
      if (/[^A-Za-z0-9]/.test(password)) score++;
      
      const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
      const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-blue-500', 'bg-green-500'];
      
      return {
        score,
        label: labels[score - 1] || '',
        color: colors[score - 1] || 'bg-gray-200',
      };
    };

    const strength = showStrength && typeof value === 'string' ? getPasswordStrength(value) : null;

    return (
      <div className="space-y-2">
        <Input
          {...props}
          ref={ref}
          type={showPassword ? 'text' : 'password'}
          value={value}
          rightIcon={
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="text-muted-foreground hover:text-foreground transition-colors"
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          }
        />
        
        {strength && strength.score > 0 && (
          <div className="space-y-1">
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((level) => (
                <div
                  key={level}
                  className={cn(
                    'h-1 flex-1 rounded-full',
                    level <= strength.score ? strength.color : 'bg-gray-200'
                  )}
                />
              ))}
            </div>
            <p className="text-xs text-muted-foreground">
              Password strength: {strength.label}
            </p>
          </div>
        )}
      </div>
    );
  }
);
PasswordInput.displayName = 'PasswordInput';

// Search Input Component
export interface SearchInputProps extends Omit<InputProps, 'type' | 'leftIcon'> {
  onSearch?: (value: string) => void;
  searchDelay?: number;
}

const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ onSearch, searchDelay = 300, onChange, ...props }, ref) => {
    const [searchValue, setSearchValue] = React.useState(props.value || '');
    const timeoutRef = React.useRef<NodeJS.Timeout>();

    React.useEffect(() => {
      if (onSearch && typeof searchValue === 'string') {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          onSearch(searchValue);
        }, searchDelay);
      }
      
      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, [searchValue, onSearch, searchDelay]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchValue(e.target.value);
      onChange?.(e);
    };

    const handleClear = () => {
      setSearchValue('');
      onSearch?.('');
      props.onClear?.();
    };

    return (
      <Input
        {...props}
        ref={ref}
        type="search"
        value={searchValue}
        onChange={handleChange}
        onClear={handleClear}
        leftIcon={<Search className="h-4 w-4" />}
        clearable
        placeholder={props.placeholder || 'Search...'}
      />
    );
  }
);
SearchInput.displayName = 'SearchInput';

// Textarea Component
export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    VariantProps<typeof inputVariants> {
  label?: string;
  error?: string;
  success?: string;
  hint?: string;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  autoResize?: boolean;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({
    className,
    variant,
    label,
    error,
    success,
    hint,
    resize = 'vertical',
    autoResize = false,
    id,
    ...props
  }, ref) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);
    const inputId = id || React.useId();
    const hasError = !!error;
    const hasSuccess = !!success && !hasError;
    const currentVariant = hasError ? 'error' : hasSuccess ? 'success' : variant;

    React.useImperativeHandle(ref, () => textareaRef.current!);

    React.useEffect(() => {
      if (autoResize && textareaRef.current) {
        const textarea = textareaRef.current;
        const adjustHeight = () => {
          textarea.style.height = 'auto';
          textarea.style.height = `${textarea.scrollHeight}px`;
        };
        
        adjustHeight();
        textarea.addEventListener('input', adjustHeight);
        
        return () => textarea.removeEventListener('input', adjustHeight);
      }
    }, [autoResize, props.value]);

    return (
      <div className="space-y-2">
        {label && (
          <label
            htmlFor={inputId}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
            {props.required && <span className="text-destructive ml-1">*</span>}
          </label>
        )}
        
        <textarea
          className={cn(
            inputVariants({ variant: currentVariant }),
            'min-h-[80px]',
            resize === 'none' && 'resize-none',
            resize === 'vertical' && 'resize-y',
            resize === 'horizontal' && 'resize-x',
            resize === 'both' && 'resize',
            className
          )}
          ref={textareaRef}
          id={inputId}
          aria-invalid={hasError}
          aria-describedby={
            error ? `${inputId}-error` : 
            success ? `${inputId}-success` : 
            hint ? `${inputId}-hint` : undefined
          }
          {...props}
        />
        
        {error && (
          <p id={`${inputId}-error`} className="text-sm text-destructive" role="alert">
            {error}
          </p>
        )}
        
        {success && !error && (
          <p id={`${inputId}-success`} className="text-sm text-green-600">
            {success}
          </p>
        )}
        
        {hint && !error && !success && (
          <p id={`${inputId}-hint`} className="text-sm text-muted-foreground">
            {hint}
          </p>
        )}
      </div>
    );
  }
);
Textarea.displayName = 'Textarea';

export { Input, PasswordInput, SearchInput, Textarea, inputVariants };
