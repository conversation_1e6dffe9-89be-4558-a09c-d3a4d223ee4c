import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { supabase } from '@/lib/supabase';
import { DatabaseService } from '@/lib/supabase';
import { RateLimitService } from '@/lib/rateLimit';
import { AnalyticsService } from '@/lib/analytics';
import { AIProviderService } from '@/lib/aiProviders';
import { validateForm } from '@/utils/validation';
import { getErrorMessage } from '@/utils/helpers';
import { ERROR_CODES, HTTP_STATUS } from '@/utils/constants';

const dbService = new DatabaseService();
const rateLimitService = new RateLimitService();
const analyticsService = new AnalyticsService();
const aiProviderService = new AIProviderService();

// Validation schemas
const generateRequestSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required').max(10000, 'Prompt too long'),
  provider: z.enum(['openai', 'anthropic', 'google', 'huggingface']).default('openai'),
  model: z.string().optional(),
  options: z.object({
    temperature: z.number().min(0).max(2).optional().default(0.7),
    maxTokens: z.number().min(1).max(4000).optional().default(1000),
    topP: z.number().min(0).max(1).optional().default(1),
    frequencyPenalty: z.number().min(-2).max(2).optional().default(0),
    presencePenalty: z.number().min(-2).max(2).optional().default(0),
    stream: z.boolean().optional().default(false),
  }).optional().default({}),
  context: z.object({
    systemPrompt: z.string().optional(),
    conversationHistory: z.array(z.object({
      role: z.enum(['user', 'assistant', 'system']),
      content: z.string(),
    })).optional(),
  }).optional(),
});

// POST /api/ai/generate - Generate AI response using specified provider
export async function POST(request: NextRequest) {
  const startTime = performance.now();

  try {
    const body = await request.json();

    // Validate request body
    const validation = generateRequestSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.VALIDATION_ERROR,
          message: 'Invalid generation request',
          details: validation.error.errors,
        },
      }, { status: HTTP_STATUS.BAD_REQUEST });
    }

    // Get user from session
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_REQUIRED,
          message: 'Authentication required for AI generation',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_INVALID,
          message: 'Invalid authentication',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    // Get user data to check subscription tier
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('subscription_tier, monthly_usage')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.DATABASE_ERROR,
          message: 'Failed to fetch user data',
        },
      }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
    }

    // Check if user has access to AI generation (pro/business only)
    if (userData.subscription_tier === 'free') {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.SUBSCRIPTION_REQUIRED,
          message: 'AI generation requires a Pro or Business subscription',
        },
      }, { status: HTTP_STATUS.FORBIDDEN });
    }

    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Rate limiting based on subscription tier
    const rateLimitResult = await rateLimitService.checkRateLimit(
      clientIP,
      'ai_generate',
      userData.subscription_tier
    );

    if (!rateLimitResult.allowed) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.RATE_LIMIT_EXCEEDED,
          message: 'Rate limit exceeded',
          retryAfter: rateLimitResult.resetTime,
        },
      }, { status: HTTP_STATUS.TOO_MANY_REQUESTS });
    }

    const { prompt, provider, model, options, context } = validation.data;

    // Check monthly usage limits
    const monthlyLimit = userData.subscription_tier === 'pro' ? 100 : 1000; // pro: 100, business: 1000
    if (userData.monthly_usage >= monthlyLimit) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.USAGE_LIMIT_EXCEEDED,
          message: `Monthly AI generation limit of ${monthlyLimit} requests exceeded`,
        },
      }, { status: HTTP_STATUS.FORBIDDEN });
    }

    // Prepare messages for AI provider
    const messages = [];
    
    if (context?.systemPrompt) {
      messages.push({
        role: 'system' as const,
        content: context.systemPrompt,
      });
    }

    if (context?.conversationHistory) {
      messages.push(...context.conversationHistory);
    }

    messages.push({
      role: 'user' as const,
      content: prompt,
    });

    // Generate AI response
    const generationResult = await aiProviderService.generateResponse({
      provider,
      model,
      messages,
      options: {
        temperature: options.temperature,
        maxTokens: options.maxTokens,
        topP: options.topP,
        frequencyPenalty: options.frequencyPenalty,
        presencePenalty: options.presencePenalty,
        stream: options.stream,
      },
    });

    if (!generationResult.success) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AI_PROVIDER_ERROR,
          message: generationResult.error || 'AI generation failed',
        },
      }, { status: HTTP_STATUS.BAD_REQUEST });
    }

    const processingTime = performance.now() - startTime;

    // Update user's monthly usage
    await supabase
      .from('users')
      .update({ monthly_usage: userData.monthly_usage + 1 })
      .eq('id', user.id);

    // Save generation to database
    const { error: saveError } = await supabase
      .from('ai_generations')
      .insert({
        user_id: user.id,
        provider,
        model: generationResult.model,
        prompt,
        response: generationResult.content,
        tokens_used: generationResult.tokensUsed,
        processing_time: Math.round(processingTime),
        metadata: {
          options,
          context,
        },
      });

    if (saveError) {
      console.error('Failed to save generation:', saveError);
      // Don't fail the request if saving fails
    }

    // Track analytics
    await analyticsService.trackEvent('ai_generation_completed', {
      provider,
      model: generationResult.model,
      promptLength: prompt.length,
      responseLength: generationResult.content?.length || 0,
      tokensUsed: generationResult.tokensUsed,
      processingTime: Math.round(processingTime),
      userId: user.id,
    });

    return NextResponse.json({
      success: true,
      data: {
        content: generationResult.content,
        model: generationResult.model,
        provider,
        tokensUsed: generationResult.tokensUsed,
        processingTime: Math.round(processingTime),
        usage: {
          monthly: userData.monthly_usage + 1,
          limit: monthlyLimit,
          remaining: monthlyLimit - userData.monthly_usage - 1,
        },
      },
    });

  } catch (error) {
    const processingTime = performance.now() - startTime;
    
    console.error('AI generation error:', error);

    // Track error
    if (error instanceof Error) {
      analyticsService.trackError(error, 'ai_generation_api');
    }

    return NextResponse.json({
      success: false,
      error: {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
    }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
  }
}

// GET /api/ai/generate - Get available AI providers and models
export async function GET(request: NextRequest) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_REQUIRED,
          message: 'Authentication required',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: {
          code: ERROR_CODES.AUTH_INVALID,
          message: 'Invalid authentication',
        },
      }, { status: HTTP_STATUS.UNAUTHORIZED });
    }

    // Get available providers and models
    const providers = await aiProviderService.getAvailableProviders();

    return NextResponse.json({
      success: true,
      data: { providers },
    });

  } catch (error) {
    console.error('AI providers GET error:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
    }, { status: HTTP_STATUS.INTERNAL_SERVER_ERROR });
  }
}
