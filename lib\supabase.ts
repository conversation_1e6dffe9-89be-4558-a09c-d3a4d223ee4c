import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/database';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Client-side Supabase client
export const createClientSupabase = () => {
  return createClientComponentClient<Database>();
};

// Server-side Supabase client
export const createServerSupabase = () => {
  return createServerComponentClient<Database>({ cookies });
};

// Service role client (for admin operations)
export const createServiceSupabase = () => {
  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
};

// Database types and interfaces
export interface User {
  id: string;
  email: string;
  created_at: string;
  subscription_tier: 'free' | 'pro' | 'business';
  monthly_usage: number;
  last_reset_date: string;
  profile?: UserProfile;
}

export interface UserProfile {
  id: string;
  user_id: string;
  full_name?: string;
  avatar_url?: string;
  website?: string;
  bio?: string;
  preferences: UserPreferences;
  created_at: string;
  updated_at: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  defaultAiProvider: string;
  enhancementLevel: 'basic' | 'advanced' | 'expert';
  emailNotifications: boolean;
  analyticsEnabled: boolean;
}

export interface PromptEnhancement {
  id: string;
  user_id: string;
  original_prompt: string;
  enhanced_prompt: string;
  ai_provider?: string;
  response_quality_score?: number;
  applied_rules: string[];
  estimated_tokens: number;
  created_at: string;
}

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  template_text: string;
  variables: string[];
  usage_count: number;
  is_public: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface UsageAnalytics {
  id: string;
  user_id: string;
  action: string;
  metadata: Record<string, any>;
  created_at: string;
}

// Database service class
export class DatabaseService {
  private supabase: SupabaseClient<Database>;

  constructor(client?: SupabaseClient<Database>) {
    this.supabase = client || createServiceSupabase();
  }

  // User management
  async createUser(email: string, additionalData?: Partial<User>): Promise<User> {
    const { data, error } = await this.supabase
      .from('users')
      .insert({
        email,
        subscription_tier: 'free',
        monthly_usage: 0,
        last_reset_date: new Date().toISOString().split('T')[0],
        ...additionalData,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getUserById(id: string): Promise<User | null> {
    const { data, error } = await this.supabase
      .from('users')
      .select(`
        *,
        profile:user_profiles(*)
      `)
      .eq('id', id)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  async updateUserUsage(userId: string, increment: number = 1): Promise<void> {
    const { error } = await this.supabase.rpc('increment_user_usage', {
      user_id: userId,
      increment_by: increment,
    });

    if (error) throw error;
  }

  async resetMonthlyUsage(userId: string): Promise<void> {
    const { error } = await this.supabase
      .from('users')
      .update({
        monthly_usage: 0,
        last_reset_date: new Date().toISOString().split('T')[0],
      })
      .eq('id', userId);

    if (error) throw error;
  }

  // Prompt enhancement management
  async savePromptEnhancement(enhancement: Omit<PromptEnhancement, 'id' | 'created_at'>): Promise<PromptEnhancement> {
    const { data, error } = await this.supabase
      .from('prompt_enhancements')
      .insert(enhancement)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getUserEnhancements(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<PromptEnhancement[]> {
    const { data, error } = await this.supabase
      .from('prompt_enhancements')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return data || [];
  }

  async getEnhancementById(id: string): Promise<PromptEnhancement | null> {
    const { data, error } = await this.supabase
      .from('prompt_enhancements')
      .select('*')
      .eq('id', id)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  // Template management
  async getPublicTemplates(category?: string): Promise<PromptTemplate[]> {
    let query = this.supabase
      .from('prompt_templates')
      .select('*')
      .eq('is_public', true)
      .order('usage_count', { ascending: false });

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  async getUserTemplates(userId: string): Promise<PromptTemplate[]> {
    const { data, error } = await this.supabase
      .from('prompt_templates')
      .select('*')
      .eq('created_by', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  async createTemplate(template: Omit<PromptTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<PromptTemplate> {
    const { data, error } = await this.supabase
      .from('prompt_templates')
      .insert(template)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async incrementTemplateUsage(templateId: string): Promise<void> {
    const { error } = await this.supabase.rpc('increment_template_usage', {
      template_id: templateId,
    });

    if (error) throw error;
  }

  // Analytics
  async logUserAction(
    userId: string,
    action: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    const { error } = await this.supabase
      .from('usage_analytics')
      .insert({
        user_id: userId,
        action,
        metadata,
      });

    if (error) console.error('Failed to log user action:', error);
  }

  async getUserAnalytics(
    userId: string,
    startDate: string,
    endDate: string
  ): Promise<UsageAnalytics[]> {
    const { data, error } = await this.supabase
      .from('usage_analytics')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('users')
        .select('id')
        .limit(1);
      
      return !error;
    } catch {
      return false;
    }
  }
}

// Utility functions
export function getSupabaseClient(context: 'client' | 'server' | 'service' = 'client'): SupabaseClient<Database> {
  switch (context) {
    case 'client':
      return createClientSupabase();
    case 'server':
      return createServerSupabase();
    case 'service':
      return createServiceSupabase();
    default:
      return createClientSupabase();
  }
}

export async function getCurrentUser(supabase?: SupabaseClient<Database>): Promise<User | null> {
  const client = supabase || createClientSupabase();
  
  const { data: { user: authUser } } = await client.auth.getUser();
  if (!authUser) return null;

  const db = new DatabaseService(client);
  return await db.getUserById(authUser.id);
}

export function isValidSubscriptionTier(tier: string): tier is 'free' | 'pro' | 'business' {
  return ['free', 'pro', 'business'].includes(tier);
}

// Database schema SQL for reference
export const DATABASE_SCHEMA = `
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  subscription_tier VARCHAR DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'business')),
  monthly_usage INTEGER DEFAULT 0,
  last_reset_date DATE DEFAULT CURRENT_DATE
);

-- User profiles table
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  full_name VARCHAR,
  avatar_url VARCHAR,
  website VARCHAR,
  bio TEXT,
  preferences JSONB DEFAULT '{"theme": "system", "defaultAiProvider": "huggingface", "enhancementLevel": "basic", "emailNotifications": true, "analyticsEnabled": true}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Prompt enhancements table
CREATE TABLE prompt_enhancements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  original_prompt TEXT NOT NULL,
  enhanced_prompt TEXT NOT NULL,
  ai_provider VARCHAR,
  response_quality_score FLOAT,
  applied_rules TEXT[] DEFAULT '{}',
  estimated_tokens INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Prompt templates table
CREATE TABLE prompt_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR NOT NULL,
  description TEXT,
  category VARCHAR NOT NULL,
  template_text TEXT NOT NULL,
  variables TEXT[] DEFAULT '{}',
  usage_count INTEGER DEFAULT 0,
  is_public BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Usage analytics table
CREATE TABLE usage_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  action VARCHAR NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);

-- Functions
CREATE OR REPLACE FUNCTION increment_user_usage(user_id UUID, increment_by INTEGER DEFAULT 1)
RETURNS VOID AS $$
BEGIN
  UPDATE users 
  SET monthly_usage = monthly_usage + increment_by 
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_template_usage(template_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE prompt_templates 
  SET usage_count = usage_count + 1 
  WHERE id = template_id;
END;
$$ LANGUAGE plpgsql;

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_prompt_enhancements_user_id ON prompt_enhancements(user_id);
CREATE INDEX idx_prompt_enhancements_created_at ON prompt_enhancements(created_at);
CREATE INDEX idx_prompt_templates_category ON prompt_templates(category);
CREATE INDEX idx_prompt_templates_public ON prompt_templates(is_public);
CREATE INDEX idx_usage_analytics_user_id ON usage_analytics(user_id);
CREATE INDEX idx_usage_analytics_created_at ON usage_analytics(created_at);
`;
