'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './useAuth';

interface RateLimitInfo {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  limit: number;
}

interface RateLimitState {
  [key: string]: RateLimitInfo;
}

interface UseRateLimitOptions {
  action: string;
  autoCheck?: boolean;
  onLimitExceeded?: (info: RateLimitInfo) => void;
  onLimitReset?: (action: string) => void;
}

interface UseRateLimitReturn {
  isAllowed: boolean;
  remaining: number;
  resetTime: number;
  limit: number;
  checkRateLimit: () => Promise<boolean>;
  getRateLimitInfo: () => RateLimitInfo | null;
  isLoading: boolean;
  error: string | null;
}

// Rate limit configurations based on subscription tiers
const RATE_LIMITS = {
  free: {
    enhance: { limit: 10, window: 3600 }, // 10 per hour
    templates_get: { limit: 50, window: 3600 }, // 50 per hour
    templates_create: { limit: 0, window: 3600 }, // Not allowed
    ai_generate: { limit: 0, window: 3600 }, // Not allowed
    analytics_track: { limit: 100, window: 3600 }, // 100 per hour
    user_get: { limit: 60, window: 3600 }, // 60 per hour
    user_update: { limit: 10, window: 3600 }, // 10 per hour
  },
  pro: {
    enhance: { limit: 100, window: 3600 }, // 100 per hour
    templates_get: { limit: 200, window: 3600 }, // 200 per hour
    templates_create: { limit: 20, window: 3600 }, // 20 per hour
    ai_generate: { limit: 50, window: 3600 }, // 50 per hour
    analytics_track: { limit: 500, window: 3600 }, // 500 per hour
    user_get: { limit: 120, window: 3600 }, // 120 per hour
    user_update: { limit: 30, window: 3600 }, // 30 per hour
  },
  business: {
    enhance: { limit: 500, window: 3600 }, // 500 per hour
    templates_get: { limit: 1000, window: 3600 }, // 1000 per hour
    templates_create: { limit: 100, window: 3600 }, // 100 per hour
    ai_generate: { limit: 200, window: 3600 }, // 200 per hour
    analytics_track: { limit: 2000, window: 3600 }, // 2000 per hour
    user_get: { limit: 300, window: 3600 }, // 300 per hour
    user_update: { limit: 100, window: 3600 }, // 100 per hour
  },
} as const;

// Local storage for client-side rate limiting
const STORAGE_KEY = 'ai-prompt-enhancer-rate-limits';

export function useRateLimit({
  action,
  autoCheck = false,
  onLimitExceeded,
  onLimitReset,
}: UseRateLimitOptions): UseRateLimitReturn {
  const { user } = useAuth();
  const [rateLimitState, setRateLimitState] = useState<RateLimitState>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Get user's subscription tier
  const subscriptionTier = user?.subscription_tier || 'free';

  // Load rate limit state from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Clean up expired entries
        const now = Date.now();
        const cleaned = Object.entries(parsed).reduce((acc, [key, value]) => {
          const info = value as RateLimitInfo;
          if (info.resetTime > now) {
            acc[key] = info;
          }
          return acc;
        }, {} as RateLimitState);
        
        setRateLimitState(cleaned);
        localStorage.setItem(STORAGE_KEY, JSON.stringify(cleaned));
      }
    } catch (error) {
      console.error('Failed to load rate limit state:', error);
    }
  }, []);

  // Save rate limit state to localStorage
  const saveRateLimitState = useCallback((newState: RateLimitState) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
    } catch (error) {
      console.error('Failed to save rate limit state:', error);
    }
  }, []);

  // Get rate limit configuration for current action and tier
  const getRateLimitConfig = useCallback(() => {
    const tierLimits = RATE_LIMITS[subscriptionTier as keyof typeof RATE_LIMITS];
    return tierLimits[action as keyof typeof tierLimits] || { limit: 10, window: 3600 };
  }, [subscriptionTier, action]);

  // Check rate limit locally
  const checkLocalRateLimit = useCallback((): RateLimitInfo => {
    const config = getRateLimitConfig();
    const now = Date.now();
    const windowStart = now - (config.window * 1000);
    
    // Get current state for this action
    const currentState = rateLimitState[action];
    
    if (!currentState || currentState.resetTime <= now) {
      // No existing limit or expired
      return {
        allowed: true,
        remaining: config.limit - 1,
        resetTime: now + (config.window * 1000),
        limit: config.limit,
      };
    }

    // Check if we're within limits
    const allowed = currentState.remaining > 0;
    
    return {
      allowed,
      remaining: Math.max(0, currentState.remaining - (allowed ? 1 : 0)),
      resetTime: currentState.resetTime,
      limit: config.limit,
    };
  }, [action, rateLimitState, getRateLimitConfig]);

  // Check rate limit with server (if authenticated)
  const checkServerRateLimit = useCallback(async (): Promise<RateLimitInfo> => {
    if (!user) {
      return checkLocalRateLimit();
    }

    try {
      const response = await fetch('/api/rate-limit/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.access_token}`,
        },
        body: JSON.stringify({
          action,
          subscriptionTier,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to check server rate limit');
      }

      const data = await response.json();
      return data.rateLimitInfo;
    } catch (error) {
      console.error('Server rate limit check failed, falling back to local:', error);
      return checkLocalRateLimit();
    }
  }, [user, action, subscriptionTier, checkLocalRateLimit]);

  // Main rate limit check function
  const checkRateLimit = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const rateLimitInfo = user ? await checkServerRateLimit() : checkLocalRateLimit();
      
      // Update local state
      const newState = {
        ...rateLimitState,
        [action]: rateLimitInfo,
      };
      setRateLimitState(newState);
      saveRateLimitState(newState);

      // Call callbacks
      if (!rateLimitInfo.allowed && onLimitExceeded) {
        onLimitExceeded(rateLimitInfo);
      }

      return rateLimitInfo.allowed;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Rate limit check failed';
      setError(errorMessage);
      console.error('Rate limit check error:', error);
      
      // Fall back to allowing the request on error
      return true;
    } finally {
      setIsLoading(false);
    }
  }, [
    user,
    action,
    rateLimitState,
    checkServerRateLimit,
    checkLocalRateLimit,
    saveRateLimitState,
    onLimitExceeded,
  ]);

  // Get current rate limit info
  const getRateLimitInfo = useCallback((): RateLimitInfo | null => {
    const currentState = rateLimitState[action];
    if (!currentState) {
      return null;
    }

    const now = Date.now();
    if (currentState.resetTime <= now) {
      return null; // Expired
    }

    return currentState;
  }, [action, rateLimitState]);

  // Auto-check on mount if enabled
  useEffect(() => {
    if (autoCheck) {
      checkRateLimit();
    }
  }, [autoCheck, checkRateLimit]);

  // Set up interval to check for reset times
  useEffect(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      const now = Date.now();
      let hasChanges = false;
      
      const newState = Object.entries(rateLimitState).reduce((acc, [key, value]) => {
        if (value.resetTime <= now) {
          hasChanges = true;
          if (onLimitReset) {
            onLimitReset(key);
          }
        } else {
          acc[key] = value;
        }
        return acc;
      }, {} as RateLimitState);

      if (hasChanges) {
        setRateLimitState(newState);
        saveRateLimitState(newState);
      }
    }, 1000); // Check every second

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [rateLimitState, onLimitReset, saveRateLimitState]);

  // Get current values
  const currentInfo = getRateLimitInfo();
  const isAllowed = currentInfo?.allowed ?? true;
  const remaining = currentInfo?.remaining ?? getRateLimitConfig().limit;
  const resetTime = currentInfo?.resetTime ?? 0;
  const limit = currentInfo?.limit ?? getRateLimitConfig().limit;

  return {
    isAllowed,
    remaining,
    resetTime,
    limit,
    checkRateLimit,
    getRateLimitInfo,
    isLoading,
    error,
  };
}
