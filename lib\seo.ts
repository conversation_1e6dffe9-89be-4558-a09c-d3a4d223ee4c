import { Metadata } from 'next';

// SEO configuration
export const SEO_CONFIG = {
  siteName: process.env.NEXT_PUBLIC_SITE_NAME || 'AI Prompt Enhancer',
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-prompt-enhancer.com',
  description: process.env.NEXT_PUBLIC_SITE_DESCRIPTION || 'Transform your AI prompts into powerful, detailed instructions that generate better responses from ChatGPT, Claude, Gemini, and other AI models.',
  keywords: [
    'AI prompt enhancer',
    'ChatGPT prompts',
    'Claude prompts',
    'Gemini prompts',
    'AI prompt optimization',
    'prompt engineering',
    'AI writing assistant',
    'prompt templates',
    'AI productivity',
    'machine learning prompts',
  ],
  author: 'Hector <PERSON>',
  twitterHandle: process.env.NEXT_PUBLIC_TWITTER_HANDLE || '@hectorta1989',
  facebookAppId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
  language: 'en',
  locale: 'en_US',
  themeColor: '#3b82f6',
};

// Page-specific SEO data
export interface PageSEO {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'product';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
}

// Generate metadata for Next.js 14
export function generateMetadata(seo: PageSEO): Metadata {
  const {
    title,
    description,
    keywords = [],
    canonical,
    noindex = false,
    nofollow = false,
    ogImage,
    ogType = 'website',
    publishedTime,
    modifiedTime,
    author,
    section,
    tags = [],
  } = seo;

  const fullTitle = title.includes(SEO_CONFIG.siteName) 
    ? title 
    : `${title} | ${SEO_CONFIG.siteName}`;

  const imageUrl = ogImage || `${SEO_CONFIG.siteUrl}/og-image.png`;
  const canonicalUrl = canonical || SEO_CONFIG.siteUrl;

  return {
    title: fullTitle,
    description,
    keywords: [...SEO_CONFIG.keywords, ...keywords].join(', '),
    authors: [{ name: author || SEO_CONFIG.author }],
    creator: SEO_CONFIG.author,
    publisher: SEO_CONFIG.siteName,
    robots: {
      index: !noindex,
      follow: !nofollow,
      googleBot: {
        index: !noindex,
        follow: !nofollow,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      type: ogType,
      locale: SEO_CONFIG.locale,
      url: canonicalUrl,
      title: fullTitle,
      description,
      siteName: SEO_CONFIG.siteName,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(section && { section }),
      ...(tags.length > 0 && { tags }),
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      creator: SEO_CONFIG.twitterHandle,
      site: SEO_CONFIG.twitterHandle,
      images: [imageUrl],
    },
    ...(SEO_CONFIG.facebookAppId && {
      facebook: {
        appId: SEO_CONFIG.facebookAppId,
      },
    }),
    other: {
      'theme-color': SEO_CONFIG.themeColor,
      'msapplication-TileColor': SEO_CONFIG.themeColor,
    },
  };
}

// Predefined page SEO configurations
export const PAGE_SEO: Record<string, PageSEO> = {
  home: {
    title: 'AI Prompt Enhancer - Transform Your AI Prompts for Better Results',
    description: 'Enhance your AI prompts with our advanced algorithms. Get better responses from ChatGPT, Claude, Gemini, and other AI models. Free prompt optimization tool.',
    keywords: ['AI prompt enhancer', 'ChatGPT prompts', 'prompt optimization', 'AI writing'],
    ogType: 'website',
  },
  dashboard: {
    title: 'Dashboard - AI Prompt Enhancer',
    description: 'Enhance your prompts, track usage, and analyze performance with our comprehensive dashboard.',
    keywords: ['prompt dashboard', 'AI analytics', 'prompt tracking'],
    noindex: true,
  },
  login: {
    title: 'Login - AI Prompt Enhancer',
    description: 'Sign in to your AI Prompt Enhancer account to access premium features and track your prompt enhancements.',
    keywords: ['login', 'sign in', 'account access'],
    noindex: true,
  },
  register: {
    title: 'Sign Up - AI Prompt Enhancer',
    description: 'Create your free AI Prompt Enhancer account and start optimizing your AI prompts today.',
    keywords: ['sign up', 'register', 'create account'],
  },
  pricing: {
    title: 'Pricing - AI Prompt Enhancer',
    description: 'Choose the perfect plan for your AI prompt enhancement needs. Free tier available with premium options.',
    keywords: ['pricing', 'plans', 'subscription', 'free tier'],
  },
  about: {
    title: 'About - AI Prompt Enhancer',
    description: 'Learn about AI Prompt Enhancer, our mission to improve AI interactions, and the team behind the platform.',
    keywords: ['about', 'team', 'mission', 'AI prompt engineering'],
  },
  blog: {
    title: 'Blog - AI Prompt Enhancer',
    description: 'Latest insights, tips, and tutorials on AI prompt engineering, optimization techniques, and best practices.',
    keywords: ['AI blog', 'prompt engineering', 'AI tips', 'tutorials'],
    ogType: 'website',
  },
  contact: {
    title: 'Contact - AI Prompt Enhancer',
    description: 'Get in touch with the AI Prompt Enhancer team. We\'re here to help with your prompt optimization needs.',
    keywords: ['contact', 'support', 'help', 'feedback'],
  },
};

// Structured data generators
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: SEO_CONFIG.siteName,
    url: SEO_CONFIG.siteUrl,
    logo: `${SEO_CONFIG.siteUrl}/logo.png`,
    description: SEO_CONFIG.description,
    founder: {
      '@type': 'Person',
      name: SEO_CONFIG.author,
    },
    sameAs: [
      `https://twitter.com/${SEO_CONFIG.twitterHandle.replace('@', '')}`,
      `https://github.com/HectorTa1989`,
    ],
  };
}

export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: SEO_CONFIG.siteName,
    url: SEO_CONFIG.siteUrl,
    description: SEO_CONFIG.description,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${SEO_CONFIG.siteUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };
}

export function generateSoftwareApplicationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: SEO_CONFIG.siteName,
    url: SEO_CONFIG.siteUrl,
    description: SEO_CONFIG.description,
    applicationCategory: 'BusinessApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '150',
      bestRating: '5',
      worstRating: '1',
    },
  };
}

export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };
}

export function generateArticleSchema(article: {
  title: string;
  description: string;
  author: string;
  publishedTime: string;
  modifiedTime?: string;
  image: string;
  url: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.description,
    image: article.image,
    author: {
      '@type': 'Person',
      name: article.author,
    },
    publisher: {
      '@type': 'Organization',
      name: SEO_CONFIG.siteName,
      logo: {
        '@type': 'ImageObject',
        url: `${SEO_CONFIG.siteUrl}/logo.png`,
      },
    },
    datePublished: article.publishedTime,
    dateModified: article.modifiedTime || article.publishedTime,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': article.url,
    },
  };
}

// SEO utility functions
export function generateCanonicalUrl(path: string): string {
  return `${SEO_CONFIG.siteUrl}${path.startsWith('/') ? path : `/${path}`}`;
}

export function generateOGImageUrl(title: string, description?: string): string {
  const params = new URLSearchParams({
    title,
    ...(description && { description }),
  });
  return `${SEO_CONFIG.siteUrl}/api/og?${params.toString()}`;
}

export function extractKeywords(text: string): string[] {
  // Simple keyword extraction (in production, use more sophisticated NLP)
  const words = text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3);
  
  const frequency: Record<string, number> = {};
  words.forEach(word => {
    frequency[word] = (frequency[word] || 0) + 1;
  });
  
  return Object.entries(frequency)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word);
}

export function generateMetaDescription(content: string, maxLength: number = 160): string {
  const cleaned = content.replace(/\s+/g, ' ').trim();
  if (cleaned.length <= maxLength) return cleaned;
  
  const truncated = cleaned.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  return lastSpace > 0 
    ? `${truncated.substring(0, lastSpace)}...`
    : `${truncated}...`;
}

// Sitemap generation
export interface SitemapEntry {
  url: string;
  lastModified?: Date;
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export function generateSitemap(entries: SitemapEntry[]): string {
  const urls = entries.map(entry => `
  <url>
    <loc>${entry.url}</loc>
    ${entry.lastModified ? `<lastmod>${entry.lastModified.toISOString()}</lastmod>` : ''}
    ${entry.changeFrequency ? `<changefreq>${entry.changeFrequency}</changefreq>` : ''}
    ${entry.priority ? `<priority>${entry.priority}</priority>` : ''}
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${urls}
</urlset>`;
}

// Robots.txt generation
export function generateRobotsTxt(): string {
  return `User-agent: *
Allow: /

# Disallow admin and private pages
Disallow: /admin/
Disallow: /api/
Disallow: /dashboard/
Disallow: /_next/

# Allow important pages
Allow: /api/sitemap
Allow: /api/robots

Sitemap: ${SEO_CONFIG.siteUrl}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1`;
}
