import { z } from 'zod';
import { VALIDATION_RULES, REGEX_PATTERNS } from './constants';

// Base validation schemas
export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Please enter a valid email address')
  .max(254, 'Email is too long');

export const passwordSchema = z
  .string()
  .min(VALIDATION_RULES.USER.PASSWORD_MIN_LENGTH, `Password must be at least ${VALIDATION_RULES.USER.PASSWORD_MIN_LENGTH} characters`)
  .max(128, 'Password is too long')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^a-zA-Z0-9]/, 'Password must contain at least one special character');

export const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .max(VALIDATION_RULES.USER.NAME_MAX_LENGTH, `Name must be less than ${VALIDATION_RULES.USER.NAME_MAX_LENGTH} characters`)
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes');

export const urlSchema = z
  .string()
  .url('Please enter a valid URL')
  .max(2048, 'URL is too long');

export const phoneSchema = z
  .string()
  .regex(REGEX_PATTERNS.PHONE, 'Please enter a valid phone number');

// User validation schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

export const registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string().min(1, 'Please confirm your password'),
  fullName: nameSchema.optional(),
  agreeToTerms: z.boolean().refine(val => val === true, {
    message: 'You must agree to the terms and conditions',
  }),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

export const resetPasswordSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string().min(1, 'Please confirm your password'),
  token: z.string().min(1, 'Reset token is required'),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string().min(1, 'Please confirm your new password'),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

// Profile validation schemas
export const profileSchema = z.object({
  fullName: nameSchema.optional(),
  bio: z.string().max(VALIDATION_RULES.USER.BIO_MAX_LENGTH, `Bio must be less than ${VALIDATION_RULES.USER.BIO_MAX_LENGTH} characters`).optional(),
  website: urlSchema.optional().or(z.literal('')),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'system']),
    defaultAiProvider: z.string().min(1, 'Please select a default AI provider'),
    enhancementLevel: z.enum(['basic', 'advanced', 'expert']),
    emailNotifications: z.boolean(),
    analyticsEnabled: z.boolean(),
  }),
});

// Prompt validation schemas
export const promptSchema = z
  .string()
  .min(VALIDATION_RULES.PROMPT.MIN_LENGTH, `Prompt must be at least ${VALIDATION_RULES.PROMPT.MIN_LENGTH} character`)
  .max(VALIDATION_RULES.PROMPT.MAX_LENGTH, `Prompt must be less than ${VALIDATION_RULES.PROMPT.MAX_LENGTH} characters`)
  .trim();

export const enhancePromptSchema = z.object({
  prompt: promptSchema,
  context: z.object({
    userLevel: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
    domain: z.string().max(100, 'Domain is too long').optional(),
    targetAI: z.string().max(50, 'Target AI is too long').optional(),
  }).optional(),
  options: z.object({
    enhancementLevel: z.enum(['basic', 'advanced', 'expert']).optional(),
    includeExamples: z.boolean().optional(),
    maxLength: z.number().min(100).max(50000).optional(),
  }).optional(),
});

// Template validation schemas
export const templateSchema = z.object({
  name: z
    .string()
    .min(VALIDATION_RULES.TEMPLATE.NAME_MIN_LENGTH, `Template name must be at least ${VALIDATION_RULES.TEMPLATE.NAME_MIN_LENGTH} characters`)
    .max(VALIDATION_RULES.TEMPLATE.NAME_MAX_LENGTH, `Template name must be less than ${VALIDATION_RULES.TEMPLATE.NAME_MAX_LENGTH} characters`),
  description: z
    .string()
    .max(VALIDATION_RULES.TEMPLATE.DESCRIPTION_MAX_LENGTH, `Description must be less than ${VALIDATION_RULES.TEMPLATE.DESCRIPTION_MAX_LENGTH} characters`)
    .optional(),
  category: z.string().min(1, 'Please select a category'),
  templateText: z
    .string()
    .min(1, 'Template text is required')
    .max(VALIDATION_RULES.TEMPLATE.TEMPLATE_MAX_LENGTH, `Template text must be less than ${VALIDATION_RULES.TEMPLATE.TEMPLATE_MAX_LENGTH} characters`),
  variables: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
});

// Contact form validation
export const contactSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject is too long'),
  message: z.string().min(10, 'Message must be at least 10 characters').max(2000, 'Message is too long'),
  category: z.enum(['general', 'support', 'billing', 'feature-request', 'bug-report']),
});

// Newsletter subscription validation
export const newsletterSchema = z.object({
  email: emailSchema,
  preferences: z.object({
    productUpdates: z.boolean().optional(),
    blogPosts: z.boolean().optional(),
    promotions: z.boolean().optional(),
  }).optional(),
});

// Search validation
export const searchSchema = z.object({
  query: z.string().min(1, 'Search query is required').max(200, 'Search query is too long'),
  category: z.string().optional(),
  filters: z.record(z.any()).optional(),
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
});

// API validation helpers
export function validateEmail(email: string): { isValid: boolean; error?: string } {
  try {
    emailSchema.parse(email);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0]?.message };
    }
    return { isValid: false, error: 'Invalid email format' };
  }
}

export function validatePassword(password: string): { 
  isValid: boolean; 
  error?: string; 
  strength: 'weak' | 'fair' | 'good' | 'strong' 
} {
  try {
    passwordSchema.parse(password);
    
    // Calculate password strength
    let score = 0;
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^a-zA-Z0-9]/.test(password)) score++;
    if (password.length >= 16) score++;
    
    const strength = score <= 2 ? 'weak' : score <= 4 ? 'fair' : score <= 6 ? 'good' : 'strong';
    
    return { isValid: true, strength };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        isValid: false, 
        error: error.errors[0]?.message,
        strength: 'weak'
      };
    }
    return { isValid: false, error: 'Invalid password format', strength: 'weak' };
  }
}

export function validateUrl(url: string): { isValid: boolean; error?: string } {
  try {
    urlSchema.parse(url);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0]?.message };
    }
    return { isValid: false, error: 'Invalid URL format' };
  }
}

// Form validation helper
export function validateForm<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: Record<string, string> } {
  try {
    const validData = schema.parse(data);
    return { success: true, data: validData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      error.errors.forEach((err) => {
        const path = err.path.join('.');
        errors[path] = err.message;
      });
      return { success: false, errors };
    }
    return { success: false, errors: { general: 'Validation failed' } };
  }
}

// Sanitization helpers
export function sanitizeHtml(input: string): string {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[^\w\s\-_.@]/g, ''); // Remove special characters except common ones
}

export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '');
}

// Custom validation functions
export function isValidSlug(slug: string): boolean {
  return REGEX_PATTERNS.SLUG.test(slug);
}

export function isValidHexColor(color: string): boolean {
  return REGEX_PATTERNS.HEX_COLOR.test(color);
}

export function isStrongPassword(password: string): boolean {
  const result = validatePassword(password);
  return result.isValid && (result.strength === 'good' || result.strength === 'strong');
}

export function isValidFileType(filename: string, allowedTypes: string[]): boolean {
  const extension = filename.toLowerCase().split('.').pop();
  return extension ? allowedTypes.includes(`.${extension}`) : false;
}

export function isValidFileSize(size: number, maxSize: number): boolean {
  return size <= maxSize;
}

// Rate limiting validation
export function isValidRateLimitWindow(window: string): boolean {
  const validWindows = ['1m', '5m', '15m', '1h', '1d', '7d', '30d'];
  return validWindows.includes(window);
}

// Export all schemas for use in components and API routes
export const schemas = {
  email: emailSchema,
  password: passwordSchema,
  name: nameSchema,
  url: urlSchema,
  phone: phoneSchema,
  login: loginSchema,
  register: registerSchema,
  forgotPassword: forgotPasswordSchema,
  resetPassword: resetPasswordSchema,
  changePassword: changePasswordSchema,
  profile: profileSchema,
  prompt: promptSchema,
  enhancePrompt: enhancePromptSchema,
  template: templateSchema,
  contact: contactSchema,
  newsletter: newsletterSchema,
  search: searchSchema,
};
