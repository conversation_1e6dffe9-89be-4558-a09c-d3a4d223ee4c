'use client';

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { X } from 'lucide-react';
import { cn } from '@/utils/helpers';

const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
        secondary:
          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive:
          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
        outline: 'text-foreground',
        success:
          'border-transparent bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100',
        warning:
          'border-transparent bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100',
        info:
          'border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100',
      },
      size: {
        sm: 'px-2 py-0.5 text-xs',
        default: 'px-2.5 py-0.5 text-xs',
        lg: 'px-3 py-1 text-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  removable?: boolean;
  onRemove?: () => void;
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant, size, removable, onRemove, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(badgeVariants({ variant, size }), className)}
        {...props}
      >
        {children}
        {removable && onRemove && (
          <button
            type="button"
            className="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10 dark:hover:bg-white/10"
            onClick={onRemove}
            aria-label="Remove badge"
          >
            <X className="h-3 w-3" />
          </button>
        )}
      </div>
    );
  }
);
Badge.displayName = 'Badge';

// Status Badge Component
export interface StatusBadgeProps extends Omit<BadgeProps, 'variant'> {
  status: 'active' | 'inactive' | 'pending' | 'error' | 'success' | 'warning';
}

const StatusBadge = React.forwardRef<HTMLDivElement, StatusBadgeProps>(
  ({ status, className, ...props }, ref) => {
    const statusVariants = {
      active: 'success',
      inactive: 'secondary',
      pending: 'warning',
      error: 'destructive',
      success: 'success',
      warning: 'warning',
    } as const;

    const statusLabels = {
      active: 'Active',
      inactive: 'Inactive',
      pending: 'Pending',
      error: 'Error',
      success: 'Success',
      warning: 'Warning',
    };

    return (
      <Badge
        ref={ref}
        variant={statusVariants[status]}
        className={cn('capitalize', className)}
        {...props}
      >
        <div className="mr-1 h-2 w-2 rounded-full bg-current" />
        {statusLabels[status]}
      </Badge>
    );
  }
);
StatusBadge.displayName = 'StatusBadge';

// Subscription Tier Badge
export interface TierBadgeProps extends Omit<BadgeProps, 'variant'> {
  tier: 'free' | 'pro' | 'business';
}

const TierBadge = React.forwardRef<HTMLDivElement, TierBadgeProps>(
  ({ tier, className, ...props }, ref) => {
    const tierVariants = {
      free: 'secondary',
      pro: 'default',
      business: 'success',
    } as const;

    const tierLabels = {
      free: 'Free',
      pro: 'Pro',
      business: 'Business',
    };

    return (
      <Badge
        ref={ref}
        variant={tierVariants[tier]}
        className={cn('capitalize', className)}
        {...props}
      >
        {tierLabels[tier]}
      </Badge>
    );
  }
);
TierBadge.displayName = 'TierBadge';

// Category Badge
export interface CategoryBadgeProps extends BadgeProps {
  category: string;
  color?: string;
}

const CategoryBadge = React.forwardRef<HTMLDivElement, CategoryBadgeProps>(
  ({ category, color, className, ...props }, ref) => {
    const style = color ? { backgroundColor: color, color: 'white' } : undefined;

    return (
      <Badge
        ref={ref}
        variant="outline"
        className={cn('capitalize', className)}
        style={style}
        {...props}
      >
        {category}
      </Badge>
    );
  }
);
CategoryBadge.displayName = 'CategoryBadge';

// Count Badge (for notifications, etc.)
export interface CountBadgeProps extends Omit<BadgeProps, 'children'> {
  count: number;
  max?: number;
  showZero?: boolean;
}

const CountBadge = React.forwardRef<HTMLDivElement, CountBadgeProps>(
  ({ count, max = 99, showZero = false, className, ...props }, ref) => {
    if (count === 0 && !showZero) {
      return null;
    }

    const displayCount = count > max ? `${max}+` : count.toString();

    return (
      <Badge
        ref={ref}
        variant="destructive"
        size="sm"
        className={cn('min-w-[1.25rem] justify-center px-1', className)}
        {...props}
      >
        {displayCount}
      </Badge>
    );
  }
);
CountBadge.displayName = 'CountBadge';

// Tag Badge (for removable tags)
export interface TagBadgeProps extends BadgeProps {
  tag: string;
  onRemove?: (tag: string) => void;
}

const TagBadge = React.forwardRef<HTMLDivElement, TagBadgeProps>(
  ({ tag, onRemove, className, ...props }, ref) => {
    return (
      <Badge
        ref={ref}
        variant="secondary"
        removable={!!onRemove}
        onRemove={() => onRemove?.(tag)}
        className={cn('cursor-default', className)}
        {...props}
      >
        {tag}
      </Badge>
    );
  }
);
TagBadge.displayName = 'TagBadge';

// Badge Group Component
export interface BadgeGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  badges: React.ReactNode[];
  max?: number;
  spacing?: 'sm' | 'default' | 'lg';
}

const BadgeGroup = React.forwardRef<HTMLDivElement, BadgeGroupProps>(
  ({ badges, max, spacing = 'default', className, ...props }, ref) => {
    const spacingClasses = {
      sm: 'gap-1',
      default: 'gap-2',
      lg: 'gap-3',
    };

    const visibleBadges = max ? badges.slice(0, max) : badges;
    const hiddenCount = max && badges.length > max ? badges.length - max : 0;

    return (
      <div
        ref={ref}
        className={cn('flex flex-wrap items-center', spacingClasses[spacing], className)}
        {...props}
      >
        {visibleBadges}
        {hiddenCount > 0 && (
          <Badge variant="outline" size="sm">
            +{hiddenCount} more
          </Badge>
        )}
      </div>
    );
  }
);
BadgeGroup.displayName = 'BadgeGroup';

export {
  Badge,
  StatusBadge,
  TierBadge,
  CategoryBadge,
  CountBadge,
  TagBadge,
  BadgeGroup,
  badgeVariants,
};
